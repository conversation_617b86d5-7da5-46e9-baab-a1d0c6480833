#!/usr/bin/env python3
"""
股票元数据API接口
提供股票元数据的管理和查询功能
"""

from fastapi import APIRouter, HTTPException, BackgroundTasks
from pydantic import BaseModel
from typing import Dict, List, Optional
import logging
from datetime import datetime

from backend.stock_metadata_manager import get_metadata_manager

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/metadata", tags=["股票元数据管理"])

# 请求模型
class MetadataUpdateRequest(BaseModel):
    market: Optional[str] = None  # 'CN', 'HK', 'US' 或 None (全部)
    force: bool = False

class MetadataQueryRequest(BaseModel):
    market: Optional[str] = None
    search: Optional[str] = None
    limit: int = 100
    offset: int = 0

# 响应模型
class MetadataResponse(BaseModel):
    success: bool
    message: str
    data: Optional[Dict] = None

class StockListResponse(BaseModel):
    success: bool
    total: int
    data: List[Dict]
    market_stats: Dict[str, int]

@router.get("/stocks", response_model=StockListResponse)
async def get_stock_list(
    market: Optional[str] = None,
    search: Optional[str] = None,
    limit: int = 100,
    offset: int = 0
):
    """获取股票列表（支持搜索和分页）"""
    try:
        metadata_manager = get_metadata_manager()
        
        # 获取缓存的股票数据
        stock_data = metadata_manager.get_cached_stock_list(market=market)
        
        # 搜索过滤
        if search:
            search_upper = search.upper()
            filtered_data = {}
            for symbol, info in stock_data.items():
                if (search_upper in symbol.upper() or 
                    search_upper in info.get('name', '').upper() or
                    search_upper in info.get('industry', '').upper()):
                    filtered_data[symbol] = info
            stock_data = filtered_data
        
        # 转换为列表格式并分页
        stock_list = []
        for symbol, info in stock_data.items():
            stock_list.append({
                'symbol': symbol,
                'name': info.get('name', ''),
                'market': info.get('market', ''),
                'industry': info.get('industry', ''),
                'ts_code': info.get('ts_code', symbol),
                'last_updated': info.get('last_updated', '')
            })
        
        # 排序
        stock_list.sort(key=lambda x: x['symbol'])
        
        # 分页
        total = len(stock_list)
        paginated_list = stock_list[offset:offset + limit]
        
        # 获取市场统计
        market_stats = metadata_manager.get_stock_count_by_market()
        
        return StockListResponse(
            success=True,
            total=total,
            data=paginated_list,
            market_stats=market_stats
        )
        
    except Exception as e:
        logger.error(f"获取股票列表失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取股票列表失败: {str(e)}")

@router.post("/update", response_model=MetadataResponse)
async def update_metadata(request: MetadataUpdateRequest, background_tasks: BackgroundTasks):
    """更新股票元数据（后台任务）"""
    try:
        metadata_manager = get_metadata_manager()
        
        if request.market:
            # 更新指定市场
            if request.force:
                # 立即执行强制更新
                result = metadata_manager.force_update_market(request.market)
                return MetadataResponse(
                    success=result['success'],
                    message=result['message'],
                    data=result
                )
            else:
                # 后台任务更新
                background_tasks.add_task(
                    metadata_manager.force_update_market, 
                    request.market
                )
                return MetadataResponse(
                    success=True,
                    message=f"已启动{request.market}市场数据更新任务"
                )
        else:
            # 更新所有市场
            if request.force:
                # 立即执行全量更新
                background_tasks.add_task(metadata_manager.scheduled_update_all_markets)
                return MetadataResponse(
                    success=True,
                    message="已启动全市场数据更新任务"
                )
            else:
                # 后台任务更新
                background_tasks.add_task(metadata_manager.scheduled_update_all_markets)
                return MetadataResponse(
                    success=True,
                    message="已启动全市场数据更新任务"
                )
                
    except Exception as e:
        logger.error(f"更新股票元数据失败: {e}")
        raise HTTPException(status_code=500, detail=f"更新股票元数据失败: {str(e)}")

@router.get("/statistics", response_model=MetadataResponse)
async def get_metadata_statistics(days: int = 7):
    """获取元数据更新统计信息"""
    try:
        metadata_manager = get_metadata_manager()
        
        # 获取更新统计
        update_stats = metadata_manager.get_update_statistics(days)
        
        # 获取市场统计
        market_stats = metadata_manager.get_stock_count_by_market()
        
        # 获取缓存状态
        cache_data = metadata_manager.get_cached_stock_list()
        cache_info = {
            'total_cached': len(cache_data),
            'cache_timestamp': metadata_manager._cache_timestamp,
            'cache_age_seconds': (
                time.time() - metadata_manager._cache_timestamp 
                if metadata_manager._cache_timestamp else None
            )
        }
        
        return MetadataResponse(
            success=True,
            message="统计信息获取成功",
            data={
                'update_statistics': update_stats,
                'market_statistics': market_stats,
                'cache_info': cache_info,
                'query_time': datetime.now().isoformat()
            }
        )
        
    except Exception as e:
        logger.error(f"获取统计信息失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取统计信息失败: {str(e)}")

@router.get("/health")
async def health_check():
    """健康检查接口"""
    try:
        metadata_manager = get_metadata_manager()
        
        # 检查数据库连接
        with metadata_manager.get_connection() as conn:
            cursor = conn.execute("SELECT COUNT(*) as count FROM stock_metadata WHERE is_active = 1")
            total_stocks = cursor.fetchone()['count']
        
        # 检查调度器状态
        scheduler_running = metadata_manager.scheduler is not None and metadata_manager.scheduler.running
        
        return {
            'status': 'healthy',
            'database_connected': True,
            'total_active_stocks': total_stocks,
            'scheduler_running': scheduler_running,
            'timestamp': datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"健康检查失败: {e}")
        return {
            'status': 'unhealthy',
            'error': str(e),
            'timestamp': datetime.now().isoformat()
        }

@router.delete("/cache")
async def clear_cache():
    """清除缓存"""
    try:
        metadata_manager = get_metadata_manager()
        
        # 清除内存缓存
        with metadata_manager.lock:
            metadata_manager._cache = {}
            metadata_manager._cache_timestamp = None
        
        return MetadataResponse(
            success=True,
            message="缓存已清除"
        )
        
    except Exception as e:
        logger.error(f"清除缓存失败: {e}")
        raise HTTPException(status_code=500, detail=f"清除缓存失败: {str(e)}")

@router.get("/search/{query}")
async def search_stocks(query: str, limit: int = 20):
    """搜索股票（快速搜索接口）"""
    try:
        metadata_manager = get_metadata_manager()
        stock_data = metadata_manager.get_cached_stock_list()
        
        query_upper = query.upper()
        suggestions = []
        
        for symbol, info in stock_data.items():
            # 匹配股票代码、名称或行业
            if (query_upper in symbol.upper() or 
                query_upper in info.get('name', '').upper() or
                query_upper in info.get('industry', '').upper()):
                
                suggestions.append({
                    'symbol': symbol,
                    'name': info.get('name', ''),
                    'market': info.get('market', ''),
                    'industry': info.get('industry', ''),
                    'match_type': 'symbol' if query_upper in symbol.upper() else 'name'
                })
                
                if len(suggestions) >= limit:
                    break
        
        # 按匹配类型排序（股票代码匹配优先）
        suggestions.sort(key=lambda x: (x['match_type'] != 'symbol', x['symbol']))
        
        return {
            'success': True,
            'query': query,
            'total': len(suggestions),
            'suggestions': suggestions
        }
        
    except Exception as e:
        logger.error(f"搜索股票失败: {e}")
        raise HTTPException(status_code=500, detail=f"搜索股票失败: {str(e)}")

# 导入time模块
import time
