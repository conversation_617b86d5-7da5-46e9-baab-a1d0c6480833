#!/usr/bin/env python3
"""
股票元数据初始化脚本
用于初始化和更新股票基本信息数据库
"""

import os
import sys
import logging
import time
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from backend.stock_metadata_manager import get_metadata_manager
from backend.data_manager import init_data_manager

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def init_stock_metadata():
    """初始化股票元数据"""
    logger.info("=" * 60)
    logger.info("🚀 开始初始化股票元数据数据库")
    logger.info("=" * 60)
    
    try:
        # 获取元数据管理器
        metadata_manager = get_metadata_manager()
        logger.info("✅ 股票元数据管理器初始化成功")
        
        # 获取数据管理器
        data_manager = init_data_manager()
        logger.info("✅ 数据管理器初始化成功")
        
        if not data_manager.akshare_data_manager:
            logger.error("❌ AkShare数据管理器未初始化，无法获取股票数据")
            return False
        
        # 初始化各市场数据
        markets = [
            ('CN', 'A股'),
            ('HK', '港股'),
            ('US', '美股')
        ]
        
        total_success = 0
        total_failed = 0
        
        for market_code, market_name in markets:
            logger.info(f"\n📊 开始初始化{market_name}数据...")
            start_time = time.time()
            
            try:
                # 获取股票列表
                logger.info(f"正在从AkShare获取{market_name}股票列表...")
                stock_df = data_manager.akshare_data_manager.download_stock_list(market_code)
                
                if stock_df.empty:
                    logger.warning(f"⚠️  未获取到{market_name}股票数据")
                    total_failed += 1
                    continue
                
                logger.info(f"📥 获取到 {len(stock_df)} 只{market_name}股票")
                
                # 保存到元数据数据库
                success_count = metadata_manager.save_stock_metadata_batch(
                    stock_df, 'akshare', market_name
                )
                
                end_time = time.time()
                duration = end_time - start_time
                
                logger.info(f"✅ {market_name}数据初始化完成:")
                logger.info(f"   - 成功保存: {success_count} 只")
                logger.info(f"   - 失败数量: {len(stock_df) - success_count} 只")
                logger.info(f"   - 耗时: {duration:.2f} 秒")
                
                total_success += success_count
                total_failed += (len(stock_df) - success_count)
                
                # 记录API状态
                metadata_manager._record_api_status(
                    'akshare', f'{market_code}_stock_list', 
                    'active' if success_count > 0 else 'error'
                )
                
                # 短暂休息，避免API限制
                time.sleep(1)
                
            except Exception as e:
                logger.error(f"❌ 初始化{market_name}数据失败: {e}")
                total_failed += 1
                metadata_manager._record_api_status(
                    'akshare', f'{market_code}_stock_list', 'error', str(e)
                )
        
        # 显示总结
        logger.info("\n" + "=" * 60)
        logger.info("📊 股票元数据初始化完成")
        logger.info("=" * 60)
        logger.info(f"✅ 总成功数量: {total_success} 只")
        logger.info(f"❌ 总失败数量: {total_failed} 只")
        
        # 获取市场统计
        market_stats = metadata_manager.get_stock_count_by_market()
        logger.info("\n📈 各市场股票数量统计:")
        for market, count in market_stats.items():
            logger.info(f"   - {market}: {count} 只")
        
        # 测试缓存功能
        logger.info("\n🧪 测试缓存功能...")
        cached_stocks = metadata_manager.get_cached_stock_list()
        logger.info(f"✅ 缓存中有 {len(cached_stocks)} 只股票")
        
        # 显示一些示例股票
        if cached_stocks:
            logger.info("\n📋 示例股票数据:")
            count = 0
            for symbol, info in cached_stocks.items():
                if count >= 5:  # 只显示前5只
                    break
                logger.info(f"   - {symbol}: {info.get('name', 'N/A')} ({info.get('market', 'N/A')})")
                count += 1
        
        logger.info("\n🎉 股票元数据初始化成功完成！")
        return True
        
    except Exception as e:
        logger.error(f"❌ 股票元数据初始化失败: {e}")
        return False

def test_metadata_functionality():
    """测试元数据功能"""
    logger.info("\n🧪 开始测试元数据功能...")
    
    try:
        metadata_manager = get_metadata_manager()
        
        # 测试搜索功能
        logger.info("测试搜索功能...")
        test_queries = ['AAPL', '平安', '腾讯', '000001']
        
        for query in test_queries:
            cached_stocks = metadata_manager.get_cached_stock_list()
            matches = []
            
            for symbol, info in cached_stocks.items():
                if (query.upper() in symbol.upper() or 
                    query in info.get('name', '') or
                    query in info.get('industry', '')):
                    matches.append((symbol, info.get('name', 'N/A')))
                    if len(matches) >= 3:  # 只显示前3个匹配
                        break
            
            if matches:
                logger.info(f"   查询 '{query}' 找到 {len(matches)} 个匹配:")
                for symbol, name in matches:
                    logger.info(f"     - {symbol}: {name}")
            else:
                logger.info(f"   查询 '{query}' 未找到匹配")
        
        # 测试统计功能
        logger.info("测试统计功能...")
        stats = metadata_manager.get_update_statistics(7)
        logger.info(f"   最近7天更新统计: {stats}")
        
        market_stats = metadata_manager.get_stock_count_by_market()
        logger.info(f"   市场统计: {market_stats}")
        
        logger.info("✅ 元数据功能测试完成")
        return True
        
    except Exception as e:
        logger.error(f"❌ 元数据功能测试失败: {e}")
        return False

def main():
    """主函数"""
    try:
        # 初始化股票元数据
        init_success = init_stock_metadata()
        
        if init_success:
            # 测试功能
            test_metadata_functionality()
            
            print("\n" + "=" * 60)
            print("🎉 股票元数据系统初始化和测试完成！")
            print("=" * 60)
            print("\n💡 使用提示:")
            print("1. 元数据已保存到数据库，支持持久化存储")
            print("2. 系统会自动定时更新股票数据（每日凌晨2点）")
            print("3. 可通过API接口 /api/metadata/* 管理元数据")
            print("4. 股票搜索功能已优化，使用缓存提高性能")
            print("5. 支持强制刷新和手动更新功能")
        else:
            print("\n❌ 股票元数据初始化失败！")
            print("请检查网络连接和API配置。")
            
    except KeyboardInterrupt:
        print("\n⚠️  用户中断操作")
    except Exception as e:
        print(f"\n❌ 程序执行失败: {e}")
        logger.error(f"程序执行失败: {e}")

if __name__ == "__main__":
    main()
