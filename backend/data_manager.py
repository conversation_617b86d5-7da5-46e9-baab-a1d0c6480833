#!/usr/bin/env python3
"""
数据管理系统
集成Tushare API和Akshare API，管理股票数据和因子数据的存储
"""

import sqlite3
import pandas as pd
import numpy as np
import tushare as ts
from typing import Dict, List, Optional, Tuple
import logging
from datetime import datetime, timedelta
import os
from contextlib import contextmanager
from dataclasses import dataclass

# 从factors.py导入FactorCalculator
try:
    from .factors import FactorCalculator # 确保从这里导入
except ImportError:
    from factors import FactorCalculator # 备用导入路径

logger = logging.getLogger(__name__)

# 尝试导入akshare
try:
    import akshare as ak
    AKSHARE_AVAILABLE = True
    logger.info("Akshare 库已导入")
except ImportError:
    AKSHARE_AVAILABLE = False
    logger.warning("Akshare 库未安装，美股数据将使用 Tushare 或模拟数据")

@dataclass
class StockInfo:
    """股票基本信息"""
    ts_code: str
    symbol: str
    name: str
    market: str
    industry: str
    created_at: datetime
    updated_at: datetime

@dataclass
class DailyData:
    """日线数据"""
    ts_code: str
    trade_date: str
    open: float
    high: float
    low: float
    close: float
    pre_close: float
    change: float
    pct_change: float
    vol: float
    amount: float
    vwap: float
    turnover_ratio: float
    total_mv: float
    pe: float
    pb: float

class DatabaseManager:
    """数据库管理器"""
    
    def __init__(self, db_path: str = "/Users/<USER>/Code/cash-flow/backend/data/financial_data.db"):
        self.db_path = db_path
        self.ensure_db_directory()
        self.init_database()
    
    def ensure_db_directory(self):
        """确保数据库目录存在"""
        db_dir = os.path.dirname(self.db_path)
        if db_dir and not os.path.exists(db_dir):
            os.makedirs(db_dir)
    
    @contextmanager
    def get_connection(self):
        """获取数据库连接"""
        conn = sqlite3.connect(self.db_path)
        conn.row_factory = sqlite3.Row  # 使结果可以按列名访问
        try:
            yield conn
        finally:
            conn.close()
    
    def init_database(self):
        """初始化数据库表结构"""
        with self.get_connection() as conn:
            # 创建股票基本信息表
            conn.execute("""
                CREATE TABLE IF NOT EXISTS stock_info (
                    ts_code TEXT PRIMARY KEY,
                    symbol TEXT NOT NULL,
                    name TEXT,
                    market TEXT,
                    industry TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            # 删除旧的 daily_data 表 (如果存在)
            conn.execute("DROP TABLE IF EXISTS daily_data")

            # 创建中国A股日线数据表
            conn.execute("""
                CREATE TABLE IF NOT EXISTS cn_daily_data (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    ts_code TEXT NOT NULL,
                    trade_date TEXT NOT NULL,
                    open REAL,
                    high REAL,
                    low REAL,
                    close REAL,
                    pre_close REAL,
                    change_amount REAL,
                    pct_change REAL,
                    vol REAL,
                    amount REAL,
                    vwap REAL,
                    turnover_ratio REAL,
                    total_mv REAL,
                    pe REAL,
                    pb REAL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    UNIQUE(ts_code, trade_date)
                )
            """)
            
            # 创建港股日线数据表
            conn.execute("""
                CREATE TABLE IF NOT EXISTS hk_daily_data (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    ts_code TEXT NOT NULL,
                    trade_date TEXT NOT NULL,
                    open REAL,
                    high REAL,
                    low REAL,
                    close REAL,
                    pre_close REAL,
                    change_amount REAL,
                    pct_change REAL,
                    vol REAL,
                    amount REAL,
                    vwap REAL,
                    turnover_ratio REAL,
                    total_mv REAL,
                    pe REAL,
                    pb REAL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    UNIQUE(ts_code, trade_date)
                )
            """)

            # 创建美股日线数据表
            conn.execute("""
                CREATE TABLE IF NOT EXISTS us_daily_data (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    ts_code TEXT NOT NULL,
                    trade_date TEXT NOT NULL,
                    open REAL,
                    high REAL,
                    low REAL,
                    close REAL,
                    pre_close REAL,
                    change_amount REAL,
                    pct_change REAL,
                    vol REAL,
                    amount REAL,
                    vwap REAL,
                    turnover_ratio REAL,
                    total_mv REAL,
                    pe REAL,
                    pb REAL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    UNIQUE(ts_code, trade_date)
                )
            """)
            
            # 创建因子数据表
            conn.execute("""
                CREATE TABLE IF NOT EXISTS factor_data (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    ts_code TEXT NOT NULL,
                    trade_date TEXT NOT NULL,
                    factor_name TEXT NOT NULL,
                    factor_value REAL,
                    calculation_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    UNIQUE(ts_code, trade_date, factor_name)
                )
            """)
            
            # 创建数据更新记录表
            conn.execute("""
                CREATE TABLE IF NOT EXISTS data_update_log (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    ts_code TEXT NOT NULL,
                    data_type TEXT NOT NULL,
                    start_date TEXT,
                    end_date TEXT,
                    record_count INTEGER,
                    status TEXT,
                    error_message TEXT,
                    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            # 创建索引以提高查询性能
            indexes = [
                "CREATE INDEX IF NOT EXISTS idx_cn_daily_data_ts_code ON cn_daily_data(ts_code)",
                "CREATE INDEX IF NOT EXISTS idx_cn_daily_data_trade_date ON cn_daily_data(trade_date)",
                "CREATE INDEX IF NOT EXISTS idx_cn_daily_data_ts_code_date ON cn_daily_data(ts_code, trade_date)",

                "CREATE INDEX IF NOT EXISTS idx_hk_daily_data_ts_code ON hk_daily_data(ts_code)",
                "CREATE INDEX IF NOT EXISTS idx_hk_daily_data_trade_date ON hk_daily_data(trade_date)",
                "CREATE INDEX IF NOT EXISTS idx_hk_daily_data_ts_code_date ON hk_daily_data(ts_code, trade_date)",

                "CREATE INDEX IF NOT EXISTS idx_us_daily_data_ts_code ON us_daily_data(ts_code)",
                "CREATE INDEX IF NOT EXISTS idx_us_daily_data_trade_date ON us_daily_data(trade_date)",
                "CREATE INDEX IF NOT EXISTS idx_us_daily_data_ts_code_date ON us_daily_data(ts_code, trade_date)",

                "CREATE INDEX IF NOT EXISTS idx_factor_data_ts_code ON factor_data(ts_code)",
                "CREATE INDEX IF NOT EXISTS idx_factor_data_trade_date ON factor_data(trade_date)",
                "CREATE INDEX IF NOT EXISTS idx_factor_data_factor_name ON factor_data(factor_name)",
                "CREATE INDEX IF NOT EXISTS idx_factor_data_composite ON factor_data(ts_code, trade_date, factor_name)"
            ]
            
            for index_sql in indexes:
                conn.execute(index_sql)
            
            conn.commit()
            logger.info("数据库初始化完成")

    def _get_market_table_name(self, ts_code: str) -> str:
        """根据ts_code判断股票所属市场并返回对应的日线数据表名"""
        if ts_code.endswith(('.SZ', '.SH')):
            return "cn_daily_data"
        elif ts_code.endswith('.HK'):
            return "hk_daily_data"
        else: # 默认为美股
            return "us_daily_data"

    def check_daily_data_coverage(self, ts_code: str, start_date: str, end_date: str, coverage_ratio: float = 0.8) -> bool:
        """
        检查数据库中某个股票在指定日期范围内的数据覆盖率是否达到要求。
        
        Args:
            ts_code (str): 股票代码。
            start_date (str): 开始日期，格式YYYYMMDD。
            end_date (str): 结束日期，格式YYYYMMDD。
            coverage_ratio (float): 期望的最低数据覆盖率 (0-1)。
            
        Returns:
            bool: 如果数据覆盖率达到要求，则返回True，否则返回False。
        """
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                
                # 根据ts_code获取目标表名
                target_table = self._get_market_table_name(ts_code)

                # 1. 查询数据库中实际存在的记录数
                cursor.execute(f"""
                    SELECT COUNT(*) FROM {target_table} 
                    WHERE ts_code = ? AND trade_date BETWEEN ? AND ?
                """, (ts_code, start_date, end_date))
                actual_count = cursor.fetchone()[0]
                
                # 2. 估算目标日期范围内的交易日数量
                start_dt = datetime.strptime(start_date, '%Y%m%d')
                end_dt = datetime.strptime(end_date, '%Y%m%d')
                
                # 考虑周末和节假日，这里先粗略估算，后续可以引入更精确的交易日计算
                # 简单估算：假设每周5个交易日
                total_days = (end_dt - start_dt).days + 1
                # 估算交易日数量，避免除以零
                estimated_trading_days = max(1, int(total_days * 5 / 7))
                
                # 3. 计算覆盖率
                current_coverage = actual_count / estimated_trading_days
                
                logger.debug(f"{ts_code} 在 {start_date}-{end_date} 的数据覆盖率: {current_coverage:.2f} (实际: {actual_count}, 估算: {estimated_trading_days})")
                
                return current_coverage >= coverage_ratio
                
        except Exception as e:
            logger.error(f"检查 {ts_code} 数据覆盖率失败: {e}")
            return False

    def get_daily_data(self, ts_code: str, start_date: str = None, end_date: str = None) -> pd.DataFrame:
        """
        从数据库获取指定股票的日线数据。
        
        Args:
            ts_code (str): 股票代码。
            start_date (str, optional): 开始日期，YYYYMMDD格式。默认为None，表示从最早日期开始。
            end_date (str, optional): 结束日期，YYYYMMDD格式。默认为None，表示到最新日期结束。
            
        Returns:
            pd.DataFrame: 股票日线数据，如果不存在则返回空DataFrame。
        """
        # 根据ts_code获取目标表名
        target_table = self._get_market_table_name(ts_code)

        query = f"SELECT trade_date, open, high, low, close, pre_close, change_amount, pct_change, vol, amount, vwap, turnover_ratio, total_mv, pe, pb FROM {target_table} WHERE ts_code = ?"
        params = [ts_code]
        
        if start_date:
            query += " AND trade_date >= ?"
            params.append(start_date)
        if end_date:
            query += " AND trade_date <= ?"
            params.append(end_date)
        
        query += " ORDER BY trade_date ASC"
        
        try:
            with self.get_connection() as conn:
                df = pd.read_sql_query(query, conn, params=params)
                
                # 确保列名正确
                df.rename(columns={'change_amount': 'change'}, inplace=True)

                # 类型转换
                for col in ['open', 'high', 'low', 'close', 'pre_close', 'change', 'pct_change', 'vol', 'amount', 'vwap', 'turnover_ratio', 'total_mv', 'pe', 'pb']:
                    if col in df.columns:
                        df[col] = pd.to_numeric(df[col], errors='coerce')
                
                # 保持trade_date为字符串格式，与数据库存储格式一致
                df['trade_date'] = df['trade_date'].astype(str)

                return df
        except Exception as e:
            logger.error(f"从数据库获取 {ts_code} 日线数据失败: {e}")
            return pd.DataFrame()

class TushareDataManager:
    """Tushare数据管理器"""
    
    def __init__(self, token: str, db_manager: DatabaseManager):
        self.token = token
        self.db_manager = db_manager
        self.pro = None
        self.init_tushare()
    
    def init_tushare(self):
        """初始化Tushare连接"""
        try:
            ts.set_token(self.token)
            self.pro = ts.pro_api()
            logger.info("Tushare API 初始化成功")
        except Exception as e:
            logger.error(f"Tushare API 初始化失败: {e}")
            raise
    
    def download_stock_list(self, market: str = 'US') -> pd.DataFrame:
        """下载股票列表"""
        try:
            if market == 'US':
                # 美股列表（需要根据实际Tushare接口调整）
                # 这里使用模拟数据，实际应该调用Tushare的美股列表接口
                stock_list = pd.DataFrame([
                    {'ts_code': 'AAPL', 'symbol': 'AAPL', 'name': 'Apple Inc.', 'market': 'NASDAQ', 'industry': 'Technology'},
                    {'ts_code': 'MSFT', 'symbol': 'MSFT', 'name': 'Microsoft Corp.', 'market': 'NASDAQ', 'industry': 'Technology'},
                    {'ts_code': 'GOOGL', 'symbol': 'GOOGL', 'name': 'Alphabet Inc.', 'market': 'NASDAQ', 'industry': 'Technology'},
                    {'ts_code': 'TSLA', 'symbol': 'TSLA', 'name': 'Tesla Inc.', 'market': 'NASDAQ', 'industry': 'Automotive'},
                    {'ts_code': 'AMZN', 'symbol': 'AMZN', 'name': 'Amazon.com Inc.', 'market': 'NASDAQ', 'industry': 'E-commerce'}
                ])
            else:
                # A股列表
                stock_list = self.pro.stock_basic(exchange='', list_status='L', fields='ts_code,symbol,name,area,industry,market')
            
            return stock_list
        except Exception as e:
            logger.error(f"下载股票列表失败: {e}")
            return pd.DataFrame()
    
    def download_daily_data(self, ts_code: str, start_date: str = None, end_date: str = None) -> pd.DataFrame:
        """下载股票日线数据"""
        try:
            # 如果未指定日期，默认获取最近1年数据
            if end_date is None:
                end_date = datetime.now().strftime('%Y%m%d')
            if start_date is None:
                start_date = (datetime.now() - timedelta(days=365)).strftime('%Y%m%d')
            
            logger.info(f"正在下载 {ts_code} 从 {start_date} 到 {end_date} 的数据")
            
            try:
                # 根据股票代码判断使用哪个接口
                if ts_code.endswith(('.SZ', '.SH')):
                    # 中国股票，使用A股日线接口
                    df = self.pro.daily(ts_code=ts_code, start_date=start_date, end_date=end_date)
                else:
                    # 美股，使用美股日线接口
                    df = self.pro.us_daily(ts_code=ts_code, start_date=start_date, end_date=end_date)
            except Exception as e:
                logger.warning(f"Tushare接口调用失败: {e}")
                # 原有：生成模拟数据作为fallback，现已移除
                df = pd.DataFrame() # 在Tushare失败时返回空DataFrame
            
            if df.empty:
                logger.warning(f"未获取到 {ts_code} 的数据")
                return pd.DataFrame()
            
            # 数据清洗和标准化
            df = self._clean_daily_data(df)
            
            return df
            
        except Exception as e:
            logger.error(f"下载 {ts_code} 日线数据失败: {e}")
            return pd.DataFrame()
    
    def _generate_mock_data(self, ts_code: str, start_date: str, end_date: str) -> pd.DataFrame:
        """生成模拟数据（用于测试）"""
        start_dt = pd.to_datetime(start_date)
        end_dt = pd.to_datetime(end_date)
        
        # 生成交易日期序列
        date_range = pd.bdate_range(start=start_dt, end=end_dt)
        
        # 模拟价格数据
        np.random.seed(hash(ts_code) % (2**32))  # 基于股票代码的固定种子
        
        num_days = len(date_range)
        if num_days == 0:
            logger.warning(f"模拟数据生成失败，日期范围 {start_date} 到 {end_date} 不包含交易日")
            return pd.DataFrame()

        data = {
            'ts_code': [ts_code] * num_days,
            'trade_date': date_range.strftime('%Y%m%d'),
            'open': np.random.uniform(90, 110, num_days),
            'high': np.random.uniform(105, 115, num_days),
            'low': np.random.uniform(85, 95, num_days),
            'close': np.random.uniform(95, 105, num_days),
            'pre_close': np.roll(np.random.uniform(95, 105, num_days), 1),
            'change': np.random.uniform(-5, 5, num_days),
            'pct_change': np.random.uniform(-5, 5, num_days),
            'vol': np.random.randint(1000000, 50000000, num_days),
            'amount': np.random.uniform(100000000, 5000000000, num_days),
            'vwap': np.random.uniform(95, 105, num_days),
            'turnover_ratio': np.random.uniform(0.5, 5.0, num_days),
            'total_mv': np.random.uniform(10000000000, 100000000000, num_days),
            'pe': np.random.uniform(10, 30, num_days),
            'pb': np.random.uniform(1, 5, num_days)
        }
        df = pd.DataFrame(data)
        
        # 确保列名标准化
        df.rename(columns={'vol': 'volume'}, inplace=True)
        
        return df
    
    def _clean_daily_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """清洗日线数据"""
        if df.empty:
            return df
        
        # 统一列名
        df.rename(columns={
            'ts_code': 'ts_code',
            'trade_date': 'trade_date',
            'open': 'open',
            'high': 'high',
            'low': 'low',
            'close': 'close',
            'pre_close': 'pre_close',
            'change': 'change_amount', # 统一列名以匹配数据库
            'pct_change': 'pct_change',
            'vol': 'vol', # 保持vol，数据库中是vol
            'amount': 'amount',
            'vwap': 'vwap',
            'turnover_ratio': 'turnover_ratio',
            'total_mv': 'total_mv',
            'pe': 'pe',
            'pb': 'pb'
        }, inplace=True)
        
        # 转换数据类型
        # 确保数值列是数值类型，非数值的设为NaN再填充0
        for col in ['open', 'high', 'low', 'close', 'pre_close', 'change_amount', 'pct_change', 'vol', 'amount', 'vwap', 'turnover_ratio', 'total_mv', 'pe', 'pb']:
            if col in df.columns:
                df[col] = pd.to_numeric(df[col], errors='coerce').fillna(0)
        
        # 处理日期格式
        if 'trade_date' in df.columns:
            df['trade_date'] = df['trade_date'].astype(str)
            
        # 填充缺失值，例如用前一个有效值填充或0填充
        df.fillna(0, inplace=True)
        
        return df

class AkshareDataManager:
    """Akshare数据管理器"""
    
    def __init__(self, db_manager: DatabaseManager):
        self.db_manager = db_manager
    
    def download_stock_list(self, market: str = 'CN') -> pd.DataFrame:
        """
        从AkShare下载股票列表
        market: 'CN' - 中国A股, 'HK' - 港股, 'US' - 美股
        """
        try:
            if market == 'CN':
                return self._download_cn_stock_list()
            elif market == 'HK':
                return self._download_hk_stock_list()
            elif market == 'US':
                return self._download_us_stock_list()
            else:
                logger.warning(f"不支持的市场类型: {market}")
                return pd.DataFrame()
        except Exception as e:
            logger.error(f"下载{market}股票列表失败: {e}")
            return pd.DataFrame()
    
    def _download_cn_stock_list(self) -> pd.DataFrame:
        """下载中国A股股票列表"""
        try:
            # 使用 stock_zh_a_spot_em 获取沪深京A股实时行情，包含股票代码和名称
            logger.info("正在从AkShare下载中国A股股票列表...")
            df = ak.stock_zh_a_spot_em()
            
            if df.empty:
                logger.warning("未从AkShare获取到A股股票列表")
                return pd.DataFrame()
            
            # 标准化列名和数据格式
            stock_list = pd.DataFrame({
                'ts_code': df['代码'].astype(str),  # 使用原始代码
                'symbol': df['代码'].astype(str),   # symbol和ts_code相同
                'name': df['名称'].astype(str),
                'market': 'CN',
                'industry': '未知'  # A股列表接口不包含行业信息，可后续补充
            })
            
            logger.info(f"成功获取 {len(stock_list)} 只A股股票信息")
            return stock_list
            
        except Exception as e:
            logger.error(f"下载A股股票列表失败: {e}")
            return pd.DataFrame()
    
    def _download_hk_stock_list(self) -> pd.DataFrame:
        """下载港股股票列表"""
        try:
            # 使用 stock_hk_famous_spot_em 获取知名港股
            logger.info("正在从AkShare下载港股股票列表...")
            df = ak.stock_hk_famous_spot_em()
            
            if df.empty:
                logger.warning("未从AkShare获取到港股股票列表")
                return pd.DataFrame()
            
            # 标准化列名和数据格式
            stock_list = pd.DataFrame({
                'ts_code': df['代码'].astype(str) + '.HK',  # 添加.HK后缀
                'symbol': df['代码'].astype(str) + '.HK',
                'name': df['名称'].astype(str),
                'market': 'HK',
                'industry': '未知'
            })
            
            logger.info(f"成功获取 {len(stock_list)} 只港股股票信息")
            return stock_list
            
        except Exception as e:
            logger.error(f"下载港股股票列表失败: {e}")
            return pd.DataFrame()
    
    def _download_us_stock_list(self) -> pd.DataFrame:
        """下载美股股票列表"""
        try:
            # 使用 stock_us_famous_spot_em 获取知名美股
            logger.info("正在从AkShare下载美股股票列表...")
            
            # 获取不同类别的知名美股
            categories = ['科技类', '金融类', '医药食品类', '媒体类', '汽车能源类', '制造零售类']
            all_stocks = []
            
            for category in categories:
                try:
                    df_category = ak.stock_us_famous_spot_em(symbol=category)
                    if not df_category.empty:
                        df_category['category'] = category
                        all_stocks.append(df_category)
                except Exception as e:
                    logger.warning(f"获取美股{category}失败: {e}")
                    continue
            
            if not all_stocks:
                logger.warning("未从AkShare获取到美股股票列表")
                return pd.DataFrame()
            
            # 合并所有类别的股票
            df = pd.concat(all_stocks, ignore_index=True)
            
            # 去重（基于代码）
            df = df.drop_duplicates(subset=['代码'])
            
            # 标准化列名和数据格式
            stock_list = pd.DataFrame({
                'ts_code': df['代码'].astype(str),
                'symbol': df['代码'].astype(str),
                'name': df['名称'].astype(str),
                'market': 'US',
                'industry': df['category'].astype(str) if 'category' in df.columns else '未知'
            })
            
            logger.info(f"成功获取 {len(stock_list)} 只美股股票信息")
            return stock_list
            
        except Exception as e:
            logger.error(f"下载美股股票列表失败: {e}")
            return pd.DataFrame()
    
    def download_cn_stock_data(self, symbol: str, start_date: str = None, end_date: str = None) -> pd.DataFrame:
        """从Akshare下载中国A股日线数据"""
        try:
            logger.info(f"正在从AkShare下载A股 {symbol} 从 {start_date} 到 {end_date} 的数据")
            
            # 使用 stock_zh_a_hist 接口（推荐使用，数据质量高）
            period = "daily"
            adjust = "qfq"  # 前复权
            
            # 构建正确的日期格式 (YYYYMMDD)
            if start_date:
                start_date = start_date.replace('-', '')
            if end_date:
                end_date = end_date.replace('-', '')
            
            df = ak.stock_zh_a_hist(symbol=symbol, period=period, 
                                   start_date=start_date, end_date=end_date, 
                                   adjust=adjust)
            
            if df.empty:
                logger.warning(f"未从AkShare获取到A股 {symbol} 的数据")
                return pd.DataFrame()
            
            # 数据清洗和标准化
            df = self._clean_akshare_cn_data(df, symbol)
            
            logger.info(f"成功从AkShare下载 {symbol} 的A股数据，共 {len(df)} 条记录")
            return df
            
        except Exception as e:
            logger.error(f"从AkShare下载A股 {symbol} 数据失败: {e}")
            return pd.DataFrame()
    
    def _clean_akshare_cn_data(self, df: pd.DataFrame, symbol: str) -> pd.DataFrame:
        """清洗和标准化AkShare中国A股数据"""
        try:
            # 确保日期列存在并转换
            if '日期' in df.columns:
                df['trade_date'] = pd.to_datetime(df['日期']).dt.strftime('%Y%m%d')
            else:
                df['trade_date'] = pd.to_datetime(df.index).strftime('%Y%m%d')
            
            # 标准化列名映射
            column_mapping = {
                '开盘': 'open',
                '收盘': 'close', 
                '最高': 'high',
                '最低': 'low',
                '成交量': 'volume',
                '成交额': 'amount',
                '振幅': 'amplitude',
                '涨跌幅': 'pct_change',
                '涨跌额': 'change',
                '换手率': 'turnover_ratio'
            }
            
            # 重命名列
            df = df.rename(columns=column_mapping)
            
            # 添加必要的列
            df['ts_code'] = symbol
            df['pre_close'] = df['close'].shift(1)  # 计算前收盘价
            
            # 处理缺失的列，设置默认值
            required_columns = ['open', 'high', 'low', 'close', 'volume', 'amount', 
                              'pct_change', 'change', 'turnover_ratio', 'pre_close']
            
            for col in required_columns:
                if col not in df.columns:
                    df[col] = np.nan
            
            # 数据类型转换和清理
            numeric_columns = ['open', 'high', 'low', 'close', 'volume', 'amount', 
                             'pct_change', 'change', 'turnover_ratio', 'pre_close']
            
            for col in numeric_columns:
                df[col] = pd.to_numeric(df[col], errors='coerce')
            
            # 计算额外指标
            df['vwap'] = df['amount'] / df['volume']  # 成交量加权平均价
            df['total_mv'] = np.nan  # 总市值暂时设为NaN
            df['pe'] = np.nan        # 市盈率暂时设为NaN  
            df['pb'] = np.nan        # 市净率暂时设为NaN
            
            # 选择需要的列
            final_columns = ['ts_code', 'trade_date', 'open', 'high', 'low', 'close',
                           'pre_close', 'change', 'pct_change', 'volume', 'amount', 
                           'vwap', 'turnover_ratio', 'total_mv', 'pe', 'pb']
            
            df = df[final_columns]
            
            # 删除包含NaN的行（主要是第一行的pre_close）
            df = df.dropna(subset=['open', 'high', 'low', 'close'])
            
            return df
            
        except Exception as e:
            logger.error(f"清洗A股数据失败: {e}")
            return pd.DataFrame()

    def get_stock_info(self, symbol: str, market_type: str = 'US') -> dict:
        """获取股票基本信息，包括行业分类"""
        try:
            if market_type == 'CN':
                return self._get_cn_stock_info(symbol)
            elif market_type == 'US':
                return self._get_us_stock_info(symbol)
            elif market_type == 'HK':
                return self._get_hk_stock_info(symbol)
            else:
                return self._get_default_stock_info(symbol, market_type)
        except Exception as e:
            logger.error(f"获取股票 {symbol} 基本信息失败: {e}")
            return self._get_default_stock_info(symbol, market_type)
    
    def _get_cn_stock_info(self, symbol: str) -> dict:
        """获取A股基本信息"""
        try:
            # 使用AkShare的个股信息查询接口
            # 注意：这里需要根据实际可用的接口调整
            # 暂时使用默认信息，可以后续扩展
            return {
                'ts_code': symbol,
                'symbol': symbol,
                'name': f'{symbol} 股票',  # 可以通过其他接口获取实际名称
                'market': 'CN',
                'industry': '未知'
            }
        except Exception as e:
            logger.error(f"获取A股 {symbol} 基本信息失败: {e}")
            return self._get_default_stock_info(symbol, 'CN')

    def _get_us_stock_info(self, symbol: str) -> dict:
        """获取美股基本信息"""
        # 美股行业分类映射
        us_industry_mapping = {
            'AMZN': {'name': 'Amazon.com Inc.', 'industry': 'E-commerce & Cloud Computing'},
            'AAPL': {'name': 'Apple Inc.', 'industry': 'Technology Hardware'},
            'MSFT': {'name': 'Microsoft Corp.', 'industry': 'Software & Cloud Services'},
            'GOOGL': {'name': 'Alphabet Inc.', 'industry': 'Internet & Technology'},
            'TSLA': {'name': 'Tesla Inc.', 'industry': 'Electric Vehicles & Energy'},
            'META': {'name': 'Meta Platforms Inc.', 'industry': 'Social Media & VR'},
            'NVDA': {'name': 'NVIDIA Corp.', 'industry': 'Semiconductors & AI'},
            'NFLX': {'name': 'Netflix Inc.', 'industry': 'Streaming & Entertainment'},
            'BABA': {'name': 'Alibaba Group', 'industry': 'E-commerce & Cloud Computing'},
            'CRM': {'name': 'Salesforce Inc.', 'industry': 'Cloud Software & CRM'},
            'ORCL': {'name': 'Oracle Corp.', 'industry': 'Database & Enterprise Software'},
            'IBM': {'name': 'IBM Corp.', 'industry': 'Technology Consulting & Cloud'},
            'ADBE': {'name': 'Adobe Inc.', 'industry': 'Digital Media & Marketing'},
            'PYPL': {'name': 'PayPal Holdings', 'industry': 'Digital Payments'},
            'INTC': {'name': 'Intel Corp.', 'industry': 'Semiconductors'},
            'AMD': {'name': 'Advanced Micro Devices', 'industry': 'Semiconductors'},
            'V': {'name': 'Visa Inc.', 'industry': 'Financial Services & Payments'},
            'MA': {'name': 'Mastercard Inc.', 'industry': 'Financial Services & Payments'},
            'DIS': {'name': 'Walt Disney Co.', 'industry': 'Entertainment & Media'},
            'UBER': {'name': 'Uber Technologies', 'industry': 'Transportation & Delivery'}
        }
        
        if symbol in us_industry_mapping:
            info = us_industry_mapping[symbol]
            return {
                'ts_code': symbol,
                'symbol': symbol,
                'name': info['name'],
                'market': 'NASDAQ',
                'industry': info['industry']
            }
        else:
            # 尝试从akshare获取（如果有相关接口的话）
            return self._get_default_stock_info(symbol, 'US')
    
    def _get_hk_stock_info(self, symbol: str) -> dict:
        """获取港股基本信息"""
        # 港股行业分类映射
        hk_industry_mapping = {
            '00700': {'name': '腾讯控股', 'industry': 'Internet & Technology'},
            '09988': {'name': '阿里巴巴-SW', 'industry': 'E-commerce & Cloud Computing'},
            '03690': {'name': '美团-W', 'industry': 'Local Services & Delivery'},
            '09618': {'name': '京东集团-SW', 'industry': 'E-commerce & Technology'},
            '02318': {'name': '中国平安', 'industry': 'Insurance & Financial Services'},
            '00941': {'name': '中国移动', 'industry': 'Telecommunications'},
            '00883': {'name': '中国海洋石油', 'industry': 'Oil & Gas'},
            '00939': {'name': '建设银行', 'industry': 'Banking & Financial Services'},
            '01398': {'name': '工商银行', 'industry': 'Banking & Financial Services'},
            '00388': {'name': '香港交易所', 'industry': 'Financial Services & Exchanges'},
            '01810': {'name': '小米集团-W', 'industry': 'Consumer Electronics & Internet'},
            '09999': {'name': '网易-S', 'industry': 'Gaming & Internet Services'},
            '02020': {'name': '安踏体育', 'industry': 'Sportswear & Apparel'},
            '01024': {'name': '快手-W', 'industry': 'Short Video & Social Media'}
        }
        
        # 提取港股代码（去掉.HK后缀）
        hk_code = symbol.split('.')[0] if '.HK' in symbol else symbol
        
        if hk_code in hk_industry_mapping:
            info = hk_industry_mapping[hk_code]
            return {
                'ts_code': symbol,
                'symbol': symbol,
                'name': info['name'],
                'market': 'HK',
                'industry': info['industry']
            }
        else:
            return self._get_default_stock_info(symbol, 'HK')
    
    def _get_default_stock_info(self, symbol: str, market_type: str) -> dict:
        """获取默认股票信息"""
        market_name = {
            'US': 'NASDAQ',
            'HK': 'HK',
            'CN': 'CN'
        }.get(market_type, 'Unknown')
        
        return {
            'ts_code': symbol,
            'symbol': symbol,
            'name': f'{symbol} Inc.' if market_type == 'US' else symbol,
            'market': market_name,
            'industry': '未知'
        }
    
    def download_us_stock_data(self, symbol: str, start_date: str = None, end_date: str = None) -> pd.DataFrame:
        """从Akshare下载美股日线数据"""
        try:
            # Akshare 美股数据目前没有复权选项，通常是前复权数据
            # symbol 示例: AAPL
            logger.info(f"正在从Akshare下载美股 {symbol} 从 {start_date} 到 {end_date} 的数据")
            df = ak.stock_us_daily(symbol=symbol, adjust="qfq").fillna(np.nan) # 使用不复权数据，并填充NaN
            
            if df.empty:
                logger.warning(f"未从Akshare获取到美股 {symbol} 的数据")
                return pd.DataFrame()

            # --- START: Standardize date column immediately after fetch ---
            # 美股akshare返回的日期列名为'date'，无需重命名
            # Ensure date column is Pandas Timestamp type
            if 'date' in df.columns and not pd.api.types.is_datetime64_any_dtype(df['date']):
                df['date'] = pd.to_datetime(df['date'])
            # --- END: Standardize date column immediately after fetch ---

            # 过滤日期范围 (now 'date' is guaranteed to be pd.Timestamp)
            if not df.empty and start_date and end_date:
                start_date_ts = pd.Timestamp(start_date)
                end_date_ts = pd.Timestamp(end_date)
                df = df[(df['date'] >= start_date_ts) &
                        (df['date'] <= end_date_ts)]

            # 数据清洗和标准化（包括将date重命名为trade_date并转换为YYYYMMDD字符串）
            df = self._clean_akshare_data(df, symbol, 'US')

            logger.info(f"US download function - df['trade_date'] dtype after clean: {df['trade_date'].dtype}")

            logger.info(f"成功从Akshare下载 {symbol} 的美股数据。")
            return df
        except Exception as e:
            logger.error(f"从Akshare下载美股 {symbol} 数据失败: {e}")
            return pd.DataFrame()

    def download_hk_stock_data(self, symbol: str, start_date: str = None, end_date: str = None) -> pd.DataFrame:
        """从Akshare下载港股日线数据"""
        try:
            # Akshare 港股数据支持后复权
            # symbol 示例: 00700.HK
            logger.info(f"正在从Akshare下载港股 {symbol} 从 {start_date} 到 {end_date} 的数据")
            # 移除 .HK 后缀以匹配akshare的symbol格式
            ak_symbol = symbol.split('.')[0]
            df = ak.stock_hk_daily(symbol=ak_symbol, adjust="qfq").fillna(np.nan) # 使用后复权数据
            
            if df.empty:
                logger.warning(f"未从Akshare获取到港股 {symbol} 的数据")
                return pd.DataFrame()
            
            # --- START: Standardize date column immediately after fetch ---
            # Unify date column name from '日期' to 'date'
            if '日期' in df.columns:
                df = df.rename(columns={'日期': 'date'})
            
            # Ensure date column is Pandas Timestamp type
            if 'date' in df.columns and not pd.api.types.is_datetime64_any_dtype(df['date']):
                df['date'] = pd.to_datetime(df['date'])
            # --- END: Standardize date column immediately after fetch ---

            # 过滤日期范围 (now 'date' is guaranteed to be pd.Timestamp)
            if not df.empty and start_date and end_date:
                start_date_ts = pd.Timestamp(start_date)
                end_date_ts = pd.Timestamp(end_date)
                df = df[(df['date'] >= start_date_ts) &
                        (df['date'] <= end_date_ts)]

            # 数据清洗和标准化（包括将date重命名为trade_date并转换为YYYYMMDD字符串）
            df = self._clean_akshare_data(df, symbol, 'HK')

            logger.info(f"HK download function - df['trade_date'] dtype after clean: {df['trade_date'].dtype}")

            logger.info(f"成功从Akshare下载 {symbol} 的港股数据。")
            return df
        except Exception as e:
            logger.error(f"从Akshare下载港股 {symbol} 数据失败: {e}")
            return pd.DataFrame()
    
    def _clean_akshare_data(self, df: pd.DataFrame, symbol: str, market_type: str) -> pd.DataFrame:
        """清洗和标准化Akshare数据"""
        # 统一列名
        df.columns = [col.lower() for col in df.columns]

        # 检查并统一日期列名，尤其针对港股可能返回'日期'的情况
        if '日期' in df.columns:
            df = df.rename(columns={'日期': 'date'})
        
        # 确保包含所有预期列
        required_cols = [
            'date', 'open', 'high', 'low', 'close', 'volume'
        ]
        for col in required_cols:
            if col not in df.columns:
                df[col] = np.nan
        
        # 将 'volume' 列重命名为 'vol'
        if 'volume' in df.columns:
            df = df.rename(columns={'volume': 'vol'})
        
        # 确保日期格式为YYYYMMDD，并在filter_by_date_range中使用datetime对象
        # df['date'] = pd.to_datetime(df['date']) # 移除此行，因为在下载函数中已处理
        
        # 计算缺失的列
        df['pre_close'] = df['close'].shift(1)
        df['change_amount'] = df['close'] - df['pre_close']
        df['pct_change'] = (df['change_amount'] / df['pre_close']) * 100
        
        # 填充NaN值，特别是对于港股和美股可能没有的字段
        for col in ['amount', 'vwap', 'turnover_ratio', 'total_mv', 'pe', 'pb']:
            if col not in df.columns:
                df[col] = np.nan
        
        # 添加ts_code
        df['ts_code'] = symbol
        
        # 调整列顺序
        df = df[[
            'ts_code', 'date', 'open', 'high', 'low', 'close', 
            'pre_close', 'change_amount', 'pct_change', 'vol', 'amount',
            'vwap', 'turnover_ratio', 'total_mv', 'pe', 'pb'
        ]]
        
        # 重命名日期列
        df = df.rename(columns={'date': 'trade_date'})
        
        # 将 trade_date 转换为 YYYYMMDD 字符串格式，以便与数据库查询格式一致
        df['trade_date'] = df['trade_date'].dt.strftime('%Y%m%d')
        
        logger.info(f"Cleaned DataFrame trade_date dtype in _clean_akshare_data: {df['trade_date'].dtype}")
        logger.info(f"Cleaned DataFrame head in _clean_akshare_data:\n{df.head(2)}")

        return df

    def _filter_by_date_range(self, df: pd.DataFrame, start_date: str = None, end_date: str = None) -> pd.DataFrame:
        """根据日期范围过滤数据"""
        if df.empty:
            return df
        
        # 如果trade_date是字符串格式（YYYYMMDD），直接进行字符串比较
        if df['trade_date'].dtype == 'object' and isinstance(df['trade_date'].iloc[0], str):
            if start_date:
                df = df[df['trade_date'] >= start_date]
            if end_date:
                df = df[df['trade_date'] <= end_date]
        else:
            # 确保日期列是datetime类型
            if not pd.api.types.is_datetime64_any_dtype(df['trade_date']):
                df['trade_date'] = pd.to_datetime(df['trade_date'])
            
            if start_date:
                start_date_dt = pd.to_datetime(start_date)
                df = df[df['trade_date'] >= start_date_dt]
            
            if end_date:
                end_date_dt = pd.to_datetime(end_date)
                df = df[df['trade_date'] <= end_date_dt]
            
        return df

    def check_data_exists(self, symbol: str, start_date: str, end_date: str) -> bool:
        """检查Akshare数据是否存在"""
        # 对于Akshare，我们不直接检查本地数据，而是假设如果能成功下载，就是存在。
        # 这里只是一个占位符，实际的逻辑在DataManager中通过智能获取处理。
        return False # 始终返回False，强制通过download方法获取

class DataManager:
    """数据管理类，负责协调数据获取、存储和因子计算"""
    def __init__(self, tushare_token: str = None, db_path: str = "/Users/<USER>/Code/cash-flow/backend/data/financial_data.db"):
        self.db_manager = DatabaseManager(db_path)

        # 初始化股票元数据管理器
        try:
            from backend.stock_metadata_manager import get_metadata_manager
            self.metadata_manager = get_metadata_manager()
            logger.info("股票元数据管理器初始化成功")
        except Exception as e:
            logger.warning(f"股票元数据管理器初始化失败: {e}")
            self.metadata_manager = None

        # 优先使用AkShare，Tushare作为可选备用
        if AKSHARE_AVAILABLE:
            self.akshare_data_manager = AkshareDataManager(self.db_manager)
            logger.info("AkShare数据管理器初始化成功 - 主要数据源")
        else:
            self.akshare_data_manager = None
            logger.warning("AkshareDataManager 未初始化，因为 Akshare 库不可用。")

        # Tushare作为备用数据源（可选）
        if tushare_token:
            self.tushare_data_manager = TushareDataManager(tushare_token, self.db_manager)
            logger.info("Tushare数据管理器初始化成功 - 备用数据源")
        else:
            self.tushare_data_manager = None
            logger.info("Tushare token未提供，跳过Tushare数据管理器初始化")

        self.factor_calculator = FactorCalculator()
        logger.info("数据管理器初始化成功 - 优先使用AkShare")

    def _is_us_stock(self, symbol: str) -> bool:
        """判断是否是美股"""
        # 美股通常是纯字母的股票代码，且不带.SH/.SZ/.HK后缀
        # 简单的判断逻辑：如果不是港股或A股，且是纯字母，则认为是美股
        if not (self._is_hk_stock(symbol) or symbol.endswith(('.SH', '.SZ'))):
            return symbol.isalpha() and symbol.isupper()
        return False
    
    def _is_hk_stock(self, symbol: str) -> bool:
        """判断是否是港股"""
        return symbol.endswith('.HK') or (symbol.isdigit() and len(symbol) == 5)
    
    def _is_cn_stock(self, symbol: str) -> bool:
        """判断是否是中国A股"""
        # A股代码通常为6位数字，或者带有.SH/.SZ后缀
        return (symbol.isdigit() and len(symbol) == 6) or symbol.endswith(('.SH', '.SZ'))

    def get_stock_data_intelligent(self, symbol: str, start_date: str = None, end_date: str = None, 
                                 force_download: bool = False) -> pd.DataFrame:
        """
        智能获取股票数据：
        1. 优先从数据库检索。
        2. 如果数据库中数据不完整或不存在，则调用相应API下载（美股/港股 Akshare，A股 Tushare）。
        3. 下载后覆盖原数据，并计算因子。
        4. 更新股票基本信息（包括行业信息）。
        """
        if start_date is None or end_date is None:
            # 默认获取最近三年的数据
            end_date = datetime.now().strftime('%Y%m%d')
            start_date = (datetime.now() - timedelta(days=3 * 365)).strftime('%Y%m%d') # 3年数据
        
        # 检查数据库中是否有足够的数据
        has_local_data = self.db_manager.check_daily_data_coverage(symbol, start_date, end_date, coverage_ratio=0.8)
        
        if has_local_data and not force_download:
            logger.info(f"从数据库获取 {symbol} 从 {start_date} 到 {end_date} 的数据")
            df = self.db_manager.get_daily_data(symbol, start_date, end_date)
            if not df.empty:
                return df
            else:
                logger.warning(f"数据库中 {symbol} 数据为空，尝试下载。")
        
        # 数据库数据不完整或需要强制下载，根据股票类型优先使用AkShare
        download_success = False
        df = pd.DataFrame()
        
        if not self.akshare_data_manager:
            logger.error("AkShare数据管理器未初始化，无法下载数据")
            return pd.DataFrame()
        
        try:
            if self._is_cn_stock(symbol):
                # 中国A股，优先使用AkShare
                logger.info(f"检测到A股代码 {symbol}，使用AkShare下载")
                df = self.akshare_data_manager.download_cn_stock_data(symbol, start_date, end_date)
                market_type = 'CN'
            elif self._is_us_stock(symbol):
                # 美股，使用AkShare
                logger.info(f"检测到美股代码 {symbol}，使用AkShare下载")
                df = self.akshare_data_manager.download_us_stock_data(symbol, start_date, end_date)
                market_type = 'US'
            elif self._is_hk_stock(symbol):
                # 港股，使用AkShare
                logger.info(f"检测到港股代码 {symbol}，使用AkShare下载")
                df = self.akshare_data_manager.download_hk_stock_data(symbol, start_date, end_date)
                market_type = 'HK'
            else:
                # 无法识别股票类型，尝试作为A股处理
                logger.warning(f"无法识别股票类型 {symbol}，尝试作为A股处理")
                df = self.akshare_data_manager.download_cn_stock_data(symbol, start_date, end_date)
                market_type = 'CN'
            
            if not df.empty:
                # 保存到数据库
                self.save_daily_data(df, symbol)
                self._calculate_and_save_factors(symbol, df)
                # 更新股票基本信息
                self._update_stock_info(symbol, market_type)
                download_success = True
                logger.info(f"成功从AkShare下载并保存 {symbol} 的数据")
            else:
                logger.warning(f"从AkShare下载 {symbol} 数据为空")
                
        except Exception as e:
            logger.error(f"从AkShare下载 {symbol} 数据失败: {e}")
            
        # 如果AkShare失败且有Tushare备用，尝试Tushare（仅对A股）
        if not download_success and self.tushare_data_manager and self._is_cn_stock(symbol):
            logger.info(f"AkShare失败，尝试使用Tushare下载A股 {symbol}")
            try:
                df = self.tushare_data_manager.download_daily_data(symbol, start_date, end_date)
                if not df.empty:
                    self.save_daily_data(df, symbol)
                    self._calculate_and_save_factors(symbol, df)
                    download_success = True
                    logger.info(f"成功从Tushare下载并保存 {symbol} 的数据")
            except Exception as e:
                logger.error(f"从Tushare下载 {symbol} 数据失败: {e}")
                self.db_manager.log_data_update(
                    symbol, 'daily_data', start_date, end_date, 0, 'FAILED', str(e)
                )

        if not download_success:
            logger.error(f"无法获取 {symbol} 从 {start_date} 到 {end_date} 的数据，请检查网络或API配置。")
            return pd.DataFrame()
        
        # 再次从数据库读取（确保获取的是最新保存的数据）
        final_df = self.db_manager.get_daily_data(symbol, start_date, end_date)
        return final_df
    
    def _update_stock_info(self, symbol: str, market_type: str):
        """更新股票基本信息到数据库"""
        try:
            if self.akshare_data_manager:
                stock_info = self.akshare_data_manager.get_stock_info(symbol, market_type)
                stock_info_df = pd.DataFrame([stock_info])
                self.save_stock_info(stock_info_df)
                logger.info(f"更新了股票 {symbol} 的基本信息: {stock_info['industry']}")
            else:
                logger.warning(f"AkshareDataManager 未初始化，无法更新 {symbol} 的基本信息")
        except Exception as e:
            logger.error(f"更新股票 {symbol} 基本信息失败: {e}")

    def initialize_data(self, stock_codes: List[str], start_date: str = None):
        """初始化数据：下载股票列表、历史数据并计算因子"""
        logger.info("开始数据初始化...")
        
        # 1. 使用AkShare下载股票基本信息
        logger.info("下载股票基本信息...")
        
        if not self.akshare_data_manager:
            logger.error("AkShare数据管理器未初始化，无法进行数据初始化")
            return
        
        all_stocks = pd.DataFrame()
        
        # 如果指定了股票代码，按市场类型分组处理
        if stock_codes:
            cn_stocks = [code for code in stock_codes if self._is_cn_stock(code)]
            us_stocks = [code for code in stock_codes if self._is_us_stock(code)]
            hk_stocks = [code for code in stock_codes if self._is_hk_stock(code)]
            
            # 下载各市场的股票列表
            try:
                if cn_stocks:
                    logger.info(f"下载{len(cn_stocks)}只A股的基本信息")
                    cn_stock_list = self.akshare_data_manager.download_stock_list('CN')
                    if not cn_stock_list.empty:
                        cn_stock_list = cn_stock_list[cn_stock_list['symbol'].isin(cn_stocks)]
                        all_stocks = pd.concat([all_stocks, cn_stock_list], ignore_index=True)
                
                if us_stocks:
                    logger.info(f"下载{len(us_stocks)}只美股的基本信息")
                    us_stock_list = self.akshare_data_manager.download_stock_list('US')
                    if not us_stock_list.empty:
                        us_stock_list = us_stock_list[us_stock_list['symbol'].isin(us_stocks)]
                        all_stocks = pd.concat([all_stocks, us_stock_list], ignore_index=True)
                
                if hk_stocks:
                    logger.info(f"下载{len(hk_stocks)}只港股的基本信息")
                    hk_stock_list = self.akshare_data_manager.download_stock_list('HK')
                    if not hk_stock_list.empty:
                        hk_stock_list = hk_stock_list[hk_stock_list['symbol'].isin(hk_stocks)]
                        all_stocks = pd.concat([all_stocks, hk_stock_list], ignore_index=True)
                        
            except Exception as e:
                logger.error(f"从AkShare下载股票列表失败: {e}")
        else:
            # 如果没有指定股票代码，下载默认的A股列表
            logger.info("下载全部A股基本信息")
            try:
                all_stocks = self.akshare_data_manager.download_stock_list('CN')
            except Exception as e:
                logger.error(f"下载A股列表失败: {e}")
        
        # 如果下载失败或没有找到匹配的股票，创建基本信息
        if all_stocks.empty and stock_codes:
            logger.warning("没有从AkShare获取到股票信息，创建基本股票信息")
            stock_info_list = []
            for code in stock_codes:
                if self._is_cn_stock(code):
                    # A股
                    symbol = code.split('.')[0] if '.' in code else code
                    stock_info_list.append({
                        'ts_code': code,
                        'symbol': symbol,
                        'name': f'{symbol}股票',
                        'market': 'CN',
                        'industry': '未知'
                    })
                elif self._is_us_stock(code):
                    # 美股
                    stock_info_list.append({
                        'ts_code': code,
                        'symbol': code,
                        'name': f'{code} Inc.',
                        'market': 'US',
                        'industry': '未知'
                    })
                elif self._is_hk_stock(code):
                    # 港股
                    symbol = code.replace('.HK', '')
                    stock_info_list.append({
                        'ts_code': code,
                        'symbol': code,
                        'name': f'{symbol}股票',
                        'market': 'HK',
                        'industry': '未知'
                    })
            all_stocks = pd.DataFrame(stock_info_list)
        
        if not all_stocks.empty:
            self.save_stock_info(all_stocks)
            logger.info(f"保存了{len(all_stocks)}只股票的基本信息")
        else:
            logger.warning("没有获取到任何股票基本信息")
        
        # 2. 下载并保存历史日线数据，然后计算因子
        logger.info("下载历史数据...")
        for index, row in all_stocks.iterrows():
            ts_code = row['ts_code']
            symbol_name = row['symbol']
            logger.info(f"正在下载 {symbol_name} 从 {start_date} 到最新日期的日线数据")
            
            # 使用智能数据获取方法
            daily_df = self.get_stock_data_intelligent(ts_code, start_date=start_date, force_download=True)
            
            if not daily_df.empty:
                logger.info(f"成功获取 {symbol_name} 的 {len(daily_df)} 条数据")
            else:
                logger.warning(f"未获取到 {symbol_name} 的日线数据，跳过因子计算")
                
        logger.info("数据初始化完成!")

    def update_data(self, stock_codes: List[str] = None):
        """更新数据：下载最新数据并更新因子"""
        logger.info("开始数据更新...")
        
        stocks_to_update = []
        if stock_codes:
            # 只更新指定股票
            stocks_in_db = self.get_available_stocks()
            for code in stock_codes:
                if code in stocks_in_db:
                    stocks_to_update.append(code)
                else:
                    logger.warning(f"股票 {code} 不在数据库中，请先初始化数据")
        else:
            # 更新所有已存在的股票
            stocks_to_update = self.get_available_stocks()

        if not stocks_to_update:
            logger.warning("没有需要更新的股票数据")
            return
            
        for ts_code in stocks_to_update:
            # 获取该股票的最新交易日期
            latest_date_in_db = self.get_latest_trade_date(ts_code)
            start_date_for_update = None
            if latest_date_in_db:
                latest_dt = datetime.strptime(latest_date_in_db, '%Y%m%d')
                start_date_for_update = (latest_dt + timedelta(days=1)).strftime('%Y%m%d')
            else:
                # 如果数据库中没有该股票数据，从1年前开始更新
                start_date_for_update = (datetime.now() - timedelta(days=365)).strftime('%Y%m%d')
            
            current_date = datetime.now().strftime('%Y%m%d')

            if start_date_for_update and start_date_for_update > current_date: # 避免未来日期
                logger.info(f"{ts_code} 已是最新数据，无需更新")
                continue

            logger.info(f"正在更新 {ts_code} 从 {start_date_for_update} 到 {current_date} 的数据")
            
            # 使用智能数据获取方法
            daily_df = self.get_stock_data_intelligent(ts_code, start_date=start_date_for_update, 
                                                     end_date=current_date, force_download=True)
            
            if not daily_df.empty:
                logger.info(f"成功更新 {ts_code} 的 {len(daily_df)} 条数据")
            else:
                logger.warning(f"未获取到 {ts_code} 的最新日线数据，跳过因子计算")
        
        logger.info("数据更新完成!")

    def save_stock_info(self, stock_list: pd.DataFrame):
        """保存股票基本信息到数据库"""
        if stock_list.empty:
            logger.warning("股票列表为空，不保存基本信息")
            return

        with self.db_manager.get_connection() as conn:
            for index, row in stock_list.iterrows():
                conn.execute("""
                    INSERT OR REPLACE INTO stock_info (
                        ts_code, symbol, name, market, industry, created_at, updated_at
                    ) VALUES (?, ?, ?, ?, ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
                """, (
                    row['ts_code'], row['symbol'], row['name'], row['market'],
                    row['industry']
                ))
            conn.commit()
        logger.info(f"保存了 {len(stock_list)} 只股票的基本信息")

    def save_daily_data(self, df: pd.DataFrame, ts_code: str, user_id: Optional[int] = None):
        """保存日线数据到数据库（支持用户隔离）"""
        if df.empty:
            logger.warning(f"{ts_code} 的日线数据为空，不保存")
            return
        
        # 确保ts_code列存在且正确
        if 'ts_code' not in df.columns or df['ts_code'].iloc[0] != ts_code:
            df['ts_code'] = ts_code

        # 根据ts_code获取目标表名
        target_table = self.db_manager._get_market_table_name(ts_code)

        # 准备数据以匹配数据库结构
        # 注意：数据库中的vol列对应df中的vol，而不是volume。此处需保持一致
        columns = ['ts_code', 'trade_date', 'open', 'high', 'low', 'close', 
                   'pre_close', 'change_amount', 'pct_change', 'vol', 'amount',
                   'vwap', 'turnover_ratio', 'total_mv', 'pe', 'pb']
        
        # 如果提供了用户ID，添加到列列表中
        if user_id is not None:
            columns.append('user_id')
            df['user_id'] = user_id
        
        # 确保所有列都存在，如果不存在则添加NaN列
        for col in columns:
            if col not in df.columns:
                if col == 'user_id':
                    df[col] = user_id
                else:
                    df[col] = np.nan
        
        # 确保trade_date是字符串格式，以避免SQLite绑定错误
        if pd.api.types.is_datetime64_any_dtype(df['trade_date']):
            df['trade_date'] = df['trade_date'].dt.strftime('%Y%m%d')
        
        logger.info(f"DataFrame dtypes before saving:\n{df.dtypes}")
        data_to_insert = df[columns].values.tolist()

        with self.db_manager.get_connection() as conn:
            if user_id is not None:
                conn.executemany(
                    f"""
                    INSERT OR REPLACE INTO {target_table} (
                        ts_code, trade_date, open, high, low, close, pre_close, 
                        change_amount, pct_change, vol, amount, vwap, turnover_ratio,
                        total_mv, pe, pb, user_id
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    """,
                    data_to_insert
                )
            else:
                conn.executemany(
                    f"""
                    INSERT OR REPLACE INTO {target_table} (
                        ts_code, trade_date, open, high, low, close, pre_close, 
                        change_amount, pct_change, vol, amount, vwap, turnover_ratio,
                        total_mv, pe, pb
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    """,
                    data_to_insert
                )
            conn.commit()
        logger.info(f"保存了 {len(df)} 条 {ts_code} 的日线数据到 {target_table} 表，用户ID: {user_id}")

    def _calculate_and_save_factors(self, ts_code: str, stock_data_df: pd.DataFrame, user_id: Optional[int] = None):
        """计算并保存指定股票的因子数据（支持用户隔离）"""
        if stock_data_df.empty:
            logger.warning(f"{ts_code} 的股票数据为空，无法计算因子")
            return
        
        # 确保数据按日期排序，以支持依赖历史数据的因子计算
        stock_data_df = stock_data_df.sort_values(by='trade_date').reset_index(drop=True)
        
        # 计算所有因子
        # 传入完整的 DataFrame
        all_factors = self.factor_calculator.calculate_all_factors(stock_data_df) # 正确调用
        
        if all_factors:
            # 因子数据关联到最新的交易日期
            latest_trade_date = stock_data_df['trade_date'].iloc[-1]
            self.save_factor_data(ts_code, latest_trade_date, all_factors, user_id)
        else:
            logger.warning(f"{ts_code} 未计算出任何因子")

    def save_factor_data(self, ts_code: str, trade_date: str, factors: Dict[str, float], user_id: Optional[int] = None):
        """保存因子数据到数据库（支持用户隔离）"""
        if not factors:
            logger.warning(f"{ts_code} {trade_date} 的因子数据为空，不保存")
            return
        
        if user_id is not None:
            data_to_insert = [
                (ts_code, trade_date, factor_name, factor_value, user_id)
                for factor_name, factor_value in factors.items()
            ]
            
            with self.db_manager.get_connection() as conn:
                conn.executemany(
                    """
                    INSERT OR REPLACE INTO factor_data (ts_code, trade_date, factor_name, factor_value, user_id, calculation_date)
                    VALUES (?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
                    """,
                    data_to_insert
                )
                conn.commit()
        else:
            data_to_insert = [
                (ts_code, trade_date, factor_name, factor_value)
                for factor_name, factor_value in factors.items()
            ]
            
            with self.db_manager.get_connection() as conn:
                conn.executemany(
                    """
                    INSERT OR REPLACE INTO factor_data (ts_code, trade_date, factor_name, factor_value, calculation_date)
                    VALUES (?, ?, ?, ?, CURRENT_TIMESTAMP)
                    """,
                    data_to_insert
                )
                conn.commit()
        logger.info(f"已保存 {ts_code} 的 {len(factors)} 个因子，用户ID: {user_id}")

    def get_stock_data(self, ts_code: str, start_date: str = None, end_date: str = None, user_id: Optional[int] = None) -> pd.DataFrame:
        """从数据库获取指定股票的日线数据（支持用户过滤）"""
        # 根据ts_code获取目标表名
        target_table = self.db_manager._get_market_table_name(ts_code)

        query = f"SELECT * FROM {target_table} WHERE ts_code = ?"
        params = [ts_code]
        
        # 添加用户过滤条件
        if user_id is not None:
            query += " AND (user_id = ? OR user_id IS NULL)"
            params.append(user_id)
        
        if start_date and end_date:
            query += " AND trade_date BETWEEN ? AND ?"
            params.extend([start_date, end_date])
        elif start_date:
            query += " AND trade_date >= ?"
            params.append(start_date)
        elif end_date:
            query += " AND trade_date <= ?"
            params.append(end_date)
            
        query += " ORDER BY trade_date ASC"

        records = []
        with self.db_manager.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute(query, tuple(params))
            records = cursor.fetchall()

        if not records:
            return pd.DataFrame()
        
        # 检查是否包含user_id列
        column_names = [description[0] for description in cursor.description]
        
        df = pd.DataFrame(records, columns=column_names)
        # 统一列名以匹配因子计算逻辑
        df.rename(columns={'vol': 'volume', 'change_amount': 'change'}, inplace=True)
        return df

    def get_factor_data(self, ts_code: str, trade_date: str = None, factor_names: List[str] = None, user_id: Optional[int] = None) -> Dict[str, float]:
        """从数据库获取指定股票和日期的因子数据（支持用户过滤）"""
        query = "SELECT factor_name, factor_value FROM factor_data WHERE ts_code = ?"
        params = [ts_code]
        
        # 添加用户过滤条件
        if user_id is not None:
            query += " AND (user_id = ? OR user_id IS NULL)"
            params.append(user_id)
        
        if trade_date:
            query += " AND trade_date = ?"
            params.append(trade_date)
        
        if factor_names:
            placeholders = ', '.join(['?' for _ in factor_names])
            query += f" AND factor_name IN ({placeholders})"
            params.extend(factor_names)
            
        records = []
        with self.db_manager.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute(query, tuple(params))
            records = cursor.fetchall()
        
        if not records:
            return {}
        
        return {row[0]: row[1] for row in records}

    def get_available_stocks(self) -> List[str]:
        """从数据库获取已保存的股票代码列表"""
        with self.db_manager.get_connection() as conn:
            records = conn.execute("SELECT DISTINCT symbol FROM stock_info").fetchall()
        return [r['symbol'] for r in records]

    def get_latest_trade_date(self, ts_code: str) -> Optional[str]:
        """获取指定股票在数据库中的最新交易日期"""
        # 根据ts_code获取目标表名
        target_table = self.db_manager._get_market_table_name(ts_code)

        with self.db_manager.get_connection() as conn:
            result = conn.execute(f"SELECT MAX(trade_date) FROM {target_table} WHERE ts_code = ?", (ts_code,)).fetchone()
        return result[0] if result and result[0] else None

def init_data_manager(tushare_token: str = None, db_path: str = "/Users/<USER>/Code/cash-flow/backend/data/financial_data.db") -> DataManager:
    """初始化并返回DataManager实例"""
    data_manager = DataManager(tushare_token, db_path)
    return data_manager 