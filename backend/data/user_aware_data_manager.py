#!/usr/bin/env python3
"""
用户感知数据管理器
包装现有DataManager，添加用户数据隔离功能
"""

import logging
from typing import Dict, List, Optional, Any
import pandas as pd
from datetime import datetime

from .abstract_data_manager import BaseUserAwareDataManager, DataAccessError, UserDataIsolationError
from backend.data_manager import DataManager

logger = logging.getLogger(__name__)

class UserAwareDataManagerProxy(BaseUserAwareDataManager):
    """用户感知数据管理器代理类"""
    
    def __init__(self, underlying_manager: DataManager):
        """
        初始化用户感知数据管理器
        
        Args:
            underlying_manager: 底层DataManager实例
        """
        super().__init__(underlying_manager)
        self.data_manager = underlying_manager
        logger.info("用户感知数据管理器代理已初始化")
    
    def get_stock_data(self, ts_code: str, start_date: str = None, end_date: str = None, 
                      user_id: Optional[int] = None) -> pd.DataFrame:
        """获取股票数据（支持用户过滤）"""
        try:
            effective_user_id = self._get_effective_user_id(user_id)
            logger.debug(f"获取股票数据: {ts_code}, 用户ID: {effective_user_id}")
            
            # 调用底层数据管理器获取数据
            df = self.data_manager.get_stock_data(ts_code, start_date, end_date)
            
            # 如果需要用户过滤且数据包含user_id列，则进行过滤
            if self._should_filter_by_user(user_id) and 'user_id' in df.columns:
                # 过滤用户数据：只返回属于当前用户或公共数据（user_id为NULL）
                filtered_df = df[
                    (df['user_id'] == effective_user_id) | 
                    (df['user_id'].isna())
                ]
                logger.debug(f"用户数据过滤完成: 原始{len(df)}条，过滤后{len(filtered_df)}条")
                return filtered_df
            
            return df
            
        except Exception as e:
            logger.error(f"获取股票数据失败: {ts_code}, 错误: {e}")
            raise DataAccessError(f"获取股票数据失败: {e}")
    
    def save_stock_data(self, df: pd.DataFrame, ts_code: str, 
                       user_id: Optional[int] = None) -> None:
        """保存股票数据（关联用户）"""
        try:
            effective_user_id = self._get_effective_user_id(user_id)
            logger.debug(f"保存股票数据: {ts_code}, 用户ID: {effective_user_id}")
            
            # 为数据添加用户ID（如果不是系统用户）
            df_with_user = df.copy()
            if not self._is_system_user(user_id):
                df_with_user['user_id'] = effective_user_id
            
            # 调用底层数据管理器保存数据
            self.data_manager.save_daily_data(df_with_user, ts_code)
            logger.info(f"股票数据保存成功: {ts_code}, 用户ID: {effective_user_id}")
            
        except Exception as e:
            logger.error(f"保存股票数据失败: {ts_code}, 错误: {e}")
            raise DataAccessError(f"保存股票数据失败: {e}")
    
    def get_factor_data(self, ts_code: str, trade_date: str = None, 
                       factor_names: List[str] = None, 
                       user_id: Optional[int] = None) -> Dict[str, float]:
        """获取因子数据（支持用户过滤）"""
        try:
            effective_user_id = self._get_effective_user_id(user_id)
            logger.debug(f"获取因子数据: {ts_code}, 用户ID: {effective_user_id}")
            
            # 如果需要用户过滤，修改底层查询
            if self._should_filter_by_user(user_id):
                # 这里需要修改底层数据管理器的查询逻辑
                # 暂时使用现有方法，后续会在数据库层面实现过滤
                factors = self.data_manager.get_factor_data(ts_code, trade_date, factor_names)
                # TODO: 实现用户级别的因子数据过滤
                return factors
            else:
                return self.data_manager.get_factor_data(ts_code, trade_date, factor_names)
            
        except Exception as e:
            logger.error(f"获取因子数据失败: {ts_code}, 错误: {e}")
            raise DataAccessError(f"获取因子数据失败: {e}")
    
    def save_factor_data(self, ts_code: str, trade_date: str, factors: Dict[str, float],
                        user_id: Optional[int] = None) -> None:
        """保存因子数据（关联用户）"""
        try:
            effective_user_id = self._get_effective_user_id(user_id)
            logger.debug(f"保存因子数据: {ts_code}, 用户ID: {effective_user_id}")
            
            # 调用底层数据管理器保存因子数据
            # TODO: 修改底层方法以支持用户ID
            self.data_manager.save_factor_data(ts_code, trade_date, factors)
            logger.info(f"因子数据保存成功: {ts_code}, 用户ID: {effective_user_id}")
            
        except Exception as e:
            logger.error(f"保存因子数据失败: {ts_code}, 错误: {e}")
            raise DataAccessError(f"保存因子数据失败: {e}")
    
    def get_available_stocks(self, user_id: Optional[int] = None) -> List[str]:
        """获取可用股票列表（支持用户过滤）"""
        try:
            effective_user_id = self._get_effective_user_id(user_id)
            logger.debug(f"获取可用股票列表, 用户ID: {effective_user_id}")
            
            # 调用底层数据管理器获取股票列表
            stocks = self.data_manager.get_available_stocks()
            
            # 如果需要用户过滤，这里可以添加用户特定的股票过滤逻辑
            # 目前返回所有可用股票
            return stocks
            
        except Exception as e:
            logger.error(f"获取可用股票列表失败, 错误: {e}")
            raise DataAccessError(f"获取可用股票列表失败: {e}")
    
    def get_user_watchlist(self, user_id: int) -> List[str]:
        """获取用户关注列表"""
        try:
            logger.debug(f"获取用户关注列表, 用户ID: {user_id}")
            
            # 查询用户关注列表
            with self.data_manager.db_manager.get_connection() as conn:
                cursor = conn.execute("""
                    SELECT ts_code FROM user_watchlist 
                    WHERE user_id = ? 
                    ORDER BY created_at DESC
                """, (user_id,))
                
                watchlist = [row[0] for row in cursor.fetchall()]
                logger.debug(f"用户关注列表获取成功: {len(watchlist)}只股票")
                return watchlist
                
        except Exception as e:
            logger.error(f"获取用户关注列表失败, 用户ID: {user_id}, 错误: {e}")
            raise DataAccessError(f"获取用户关注列表失败: {e}")
    
    def add_to_watchlist(self, user_id: int, ts_code: str) -> bool:
        """添加股票到用户关注列表"""
        try:
            logger.debug(f"添加股票到关注列表: {ts_code}, 用户ID: {user_id}")
            
            with self.data_manager.db_manager.get_connection() as conn:
                # 检查是否已存在
                cursor = conn.execute("""
                    SELECT COUNT(*) FROM user_watchlist 
                    WHERE user_id = ? AND ts_code = ?
                """, (user_id, ts_code))
                
                if cursor.fetchone()[0] > 0:
                    logger.info(f"股票已在关注列表中: {ts_code}")
                    return False
                
                # 添加到关注列表
                conn.execute("""
                    INSERT INTO user_watchlist (user_id, ts_code, created_at)
                    VALUES (?, ?, ?)
                """, (user_id, ts_code, datetime.now()))
                
                conn.commit()
                logger.info(f"股票添加到关注列表成功: {ts_code}")
                return True
                
        except Exception as e:
            logger.error(f"添加股票到关注列表失败: {ts_code}, 用户ID: {user_id}, 错误: {e}")
            raise DataAccessError(f"添加股票到关注列表失败: {e}")
    
    def remove_from_watchlist(self, user_id: int, ts_code: str) -> bool:
        """从用户关注列表移除股票"""
        try:
            logger.debug(f"从关注列表移除股票: {ts_code}, 用户ID: {user_id}")
            
            with self.data_manager.db_manager.get_connection() as conn:
                cursor = conn.execute("""
                    DELETE FROM user_watchlist 
                    WHERE user_id = ? AND ts_code = ?
                """, (user_id, ts_code))
                
                conn.commit()
                
                if cursor.rowcount > 0:
                    logger.info(f"股票从关注列表移除成功: {ts_code}")
                    return True
                else:
                    logger.info(f"股票不在关注列表中: {ts_code}")
                    return False
                    
        except Exception as e:
            logger.error(f"从关注列表移除股票失败: {ts_code}, 用户ID: {user_id}, 错误: {e}")
            raise DataAccessError(f"从关注列表移除股票失败: {e}")
    
    def get_latest_trade_date(self, ts_code: str, user_id: Optional[int] = None) -> Optional[str]:
        """获取最新交易日期（支持用户过滤）"""
        try:
            effective_user_id = self._get_effective_user_id(user_id)
            logger.debug(f"获取最新交易日期: {ts_code}, 用户ID: {effective_user_id}")
            
            # 调用底层数据管理器
            return self.data_manager.get_latest_trade_date(ts_code)
            
        except Exception as e:
            logger.error(f"获取最新交易日期失败: {ts_code}, 错误: {e}")
            raise DataAccessError(f"获取最新交易日期失败: {e}")

def create_user_aware_data_manager(underlying_manager: DataManager) -> UserAwareDataManagerProxy:
    """创建用户感知数据管理器的工厂函数"""
    return UserAwareDataManagerProxy(underlying_manager) 