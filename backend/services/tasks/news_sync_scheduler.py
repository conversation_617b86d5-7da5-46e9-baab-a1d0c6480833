#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
财经新闻定时同步任务模块
负责定时获取和同步财经新闻数据
"""

import logging
import asyncio
from datetime import datetime, timedelta
from typing import Dict, Any, Optional
from apscheduler.schedulers.asyncio import AsyncIOScheduler
from apscheduler.triggers.interval import IntervalTrigger
from apscheduler.triggers.cron import CronTrigger
from apscheduler.jobstores.memory import MemoryJobStore
from apscheduler.executors.asyncio import AsyncIOExecutor

from backend.financial_news_manager import get_financial_news_manager
from backend.ai.tools.akshare.financial_news import (
    akshare_financial_breakfast_tool,
    akshare_financial_global_news_tool,
    akshare_financial_comprehensive_tool
)

logger = logging.getLogger(__name__)

class NewsyncScheduler:
    """财经新闻同步调度器"""
    
    def __init__(self):
        """初始化调度器"""
        # 配置调度器
        jobstores = {
            'default': MemoryJobStore()
        }
        executors = {
            'default': AsyncIOExecutor()
        }
        job_defaults = {
            'coalesce': False,
            'max_instances': 1,
            'misfire_grace_time': 300  # 5分钟容错时间
        }
        
        self.scheduler = AsyncIOScheduler(
            jobstores=jobstores,
            executors=executors,
            job_defaults=job_defaults,
            timezone='Asia/Shanghai'
        )
        
        self.is_running = False
        self.task_status = {}
        
        # 获取数据库管理器
        self.news_manager = get_financial_news_manager()
        
        logger.info("财经新闻同步调度器初始化完成")
    
    async def sync_financial_breakfast(self):
        """同步财经早餐数据"""
        task_name = "sync_financial_breakfast"
        start_time = datetime.now()
        
        try:
            logger.info("开始同步财经早餐数据...")
            self.task_status[task_name] = {
                'status': 'running',
                'start_time': start_time.isoformat(),
                'message': '正在同步财经早餐数据...'
            }
            
            # 调用财经早餐工具
            result = akshare_financial_breakfast_tool.invoke({})
            
            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()
            
            self.task_status[task_name] = {
                'status': 'completed',
                'start_time': start_time.isoformat(),
                'end_time': end_time.isoformat(),
                'duration': duration,
                'result': result,
                'message': '财经早餐数据同步完成'
            }
            
            logger.info(f"财经早餐数据同步完成，耗时: {duration:.2f}秒")
            
        except Exception as e:
            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()
            error_msg = f"财经早餐数据同步失败: {str(e)}"
            
            self.task_status[task_name] = {
                'status': 'failed',
                'start_time': start_time.isoformat(),
                'end_time': end_time.isoformat(),
                'duration': duration,
                'error': error_msg,
                'message': error_msg
            }
            
            logger.error(error_msg)
    
    async def sync_global_news(self):
        """同步全球财经快讯"""
        task_name = "sync_global_news"
        start_time = datetime.now()
        
        try:
            logger.info("开始同步全球财经快讯...")
            self.task_status[task_name] = {
                'status': 'running',
                'start_time': start_time.isoformat(),
                'message': '正在同步全球财经快讯...'
            }
            
            # 调用全球财经快讯工具
            result = akshare_financial_global_news_tool.invoke({
                'sources': '',  # 获取所有源
                'max_workers': 3
            })
            
            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()
            
            self.task_status[task_name] = {
                'status': 'completed',
                'start_time': start_time.isoformat(),
                'end_time': end_time.isoformat(),
                'duration': duration,
                'result': result,
                'message': '全球财经快讯同步完成'
            }
            
            logger.info(f"全球财经快讯同步完成，耗时: {duration:.2f}秒")
            
        except Exception as e:
            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()
            error_msg = f"全球财经快讯同步失败: {str(e)}"
            
            self.task_status[task_name] = {
                'status': 'failed',
                'start_time': start_time.isoformat(),
                'end_time': end_time.isoformat(),
                'duration': duration,
                'error': error_msg,
                'message': error_msg
            }
            
            logger.error(error_msg)
    
    async def sync_comprehensive_news(self):
        """同步综合财经资讯"""
        task_name = "sync_comprehensive_news"
        start_time = datetime.now()
        
        try:
            logger.info("开始同步综合财经资讯...")
            self.task_status[task_name] = {
                'status': 'running',
                'start_time': start_time.isoformat(),
                'message': '正在同步综合财经资讯...'
            }
            
            # 调用综合财经资讯工具
            result = akshare_financial_comprehensive_tool.invoke({
                'include_breakfast': True,
                'include_global': True,
                'include_broker': False,  # 证券原创更新频率较低
                'max_workers': 5
            })
            
            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()
            
            self.task_status[task_name] = {
                'status': 'completed',
                'start_time': start_time.isoformat(),
                'end_time': end_time.isoformat(),
                'duration': duration,
                'result': result,
                'message': '综合财经资讯同步完成'
            }
            
            logger.info(f"综合财经资讯同步完成，耗时: {duration:.2f}秒")
            
        except Exception as e:
            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()
            error_msg = f"综合财经资讯同步失败: {str(e)}"
            
            self.task_status[task_name] = {
                'status': 'failed',
                'start_time': start_time.isoformat(),
                'end_time': end_time.isoformat(),
                'duration': duration,
                'error': error_msg,
                'message': error_msg
            }
            
            logger.error(error_msg)
    
    async def cleanup_old_news(self):
        """清理过期新闻数据"""
        task_name = "cleanup_old_news"
        start_time = datetime.now()
        
        try:
            logger.info("开始清理过期新闻数据...")
            self.task_status[task_name] = {
                'status': 'running',
                'start_time': start_time.isoformat(),
                'message': '正在清理过期新闻数据...'
            }
            
            # 清理30天前的新闻
            deleted_count = self.news_manager.cleanup_old_news(days_to_keep=30)
            
            # 执行去重操作
            dedupe_count = self.news_manager.deduplicate_news()
            
            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()
            
            self.task_status[task_name] = {
                'status': 'completed',
                'start_time': start_time.isoformat(),
                'end_time': end_time.isoformat(),
                'duration': duration,
                'deleted_count': deleted_count,
                'dedupe_count': dedupe_count,
                'message': f'清理完成: 删除{deleted_count}条过期记录，去重{dedupe_count}条重复记录'
            }
            
            logger.info(f"新闻数据清理完成，删除{deleted_count}条过期记录，去重{dedupe_count}条重复记录")
            
        except Exception as e:
            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()
            error_msg = f"新闻数据清理失败: {str(e)}"
            
            self.task_status[task_name] = {
                'status': 'failed',
                'start_time': start_time.isoformat(),
                'end_time': end_time.isoformat(),
                'duration': duration,
                'error': error_msg,
                'message': error_msg
            }
            
            logger.error(error_msg)
    
    async def manual_sync_all(self):
        """手动同步所有数据"""
        task_name = "manual_sync_all"
        start_time = datetime.now()
        
        try:
            logger.info("开始手动同步所有财经新闻数据...")
            self.task_status[task_name] = {
                'status': 'running',
                'start_time': start_time.isoformat(),
                'message': '正在手动同步所有财经新闻数据...'
            }
            
            # 并发执行所有同步任务
            await asyncio.gather(
                self.sync_financial_breakfast(),
                self.sync_global_news(),
                return_exceptions=True
            )
            
            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()
            
            self.task_status[task_name] = {
                'status': 'completed',
                'start_time': start_time.isoformat(),
                'end_time': end_time.isoformat(),
                'duration': duration,
                'message': '手动同步所有数据完成'
            }
            
            logger.info(f"手动同步所有数据完成，耗时: {duration:.2f}秒")
            
        except Exception as e:
            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()
            error_msg = f"手动同步所有数据失败: {str(e)}"
            
            self.task_status[task_name] = {
                'status': 'failed',
                'start_time': start_time.isoformat(),
                'end_time': end_time.isoformat(),
                'duration': duration,
                'error': error_msg,
                'message': error_msg
            }
            
            logger.error(error_msg)
    
    def setup_jobs(self):
        """设置定时任务"""
        try:
            # 1. 全球财经快讯 - 每5分钟同步一次
            self.scheduler.add_job(
                self.sync_global_news,
                trigger=IntervalTrigger(minutes=5),
                id='sync_global_news',
                name='同步全球财经快讯',
                replace_existing=True
            )
            
            # 2. 财经早餐 - 每30分钟同步一次
            self.scheduler.add_job(
                self.sync_financial_breakfast,
                trigger=IntervalTrigger(minutes=30),
                id='sync_financial_breakfast',
                name='同步财经早餐',
                replace_existing=True
            )
            
            # 3. 综合财经资讯 - 每15分钟同步一次
            self.scheduler.add_job(
                self.sync_comprehensive_news,
                trigger=IntervalTrigger(minutes=15),
                id='sync_comprehensive_news',
                name='同步综合财经资讯',
                replace_existing=True
            )
            
            # 4. 清理过期数据 - 每天凌晨2点执行
            self.scheduler.add_job(
                self.cleanup_old_news,
                trigger=CronTrigger(hour=2, minute=0),
                id='cleanup_old_news',
                name='清理过期新闻数据',
                replace_existing=True
            )
            
            logger.info("定时任务设置完成")
            
        except Exception as e:
            logger.error(f"设置定时任务失败: {e}")
            raise
    
    async def start(self):
        """启动调度器"""
        try:
            if not self.is_running:
                self.setup_jobs()
                self.scheduler.start()
                self.is_running = True
                logger.info("财经新闻同步调度器启动成功")
                
                # 启动时立即执行一次同步
                await self.manual_sync_all()
            else:
                logger.warning("调度器已经在运行中")
                
        except Exception as e:
            logger.error(f"启动调度器失败: {e}")
            raise
    
    async def stop(self):
        """停止调度器"""
        try:
            if self.is_running:
                self.scheduler.shutdown(wait=True)
                self.is_running = False
                logger.info("财经新闻同步调度器已停止")
            else:
                logger.warning("调度器未在运行")
                
        except Exception as e:
            logger.error(f"停止调度器失败: {e}")
            raise
    
    def get_job_status(self, job_id: str = None) -> Dict[str, Any]:
        """获取任务状态"""
        if job_id:
            return self.task_status.get(job_id, {})
        else:
            return self.task_status
    
    def get_scheduler_status(self) -> Dict[str, Any]:
        """获取调度器状态"""
        jobs = []
        if self.is_running:
            for job in self.scheduler.get_jobs():
                jobs.append({
                    'id': job.id,
                    'name': job.name,
                    'next_run_time': job.next_run_time.isoformat() if job.next_run_time else None,
                    'trigger': str(job.trigger)
                })
        
        return {
            'is_running': self.is_running,
            'jobs': jobs,
            'task_status': self.task_status
        }
    
    async def trigger_job(self, job_id: str) -> Dict[str, Any]:
        """手动触发指定任务"""
        try:
            if not self.is_running:
                return {'error': '调度器未运行'}
            
            job = self.scheduler.get_job(job_id)
            if not job:
                return {'error': f'任务 {job_id} 不存在'}
            
            # 手动执行任务
            if job_id == 'sync_global_news':
                await self.sync_global_news()
            elif job_id == 'sync_financial_breakfast':
                await self.sync_financial_breakfast()
            elif job_id == 'sync_comprehensive_news':
                await self.sync_comprehensive_news()
            elif job_id == 'cleanup_old_news':
                await self.cleanup_old_news()
            else:
                return {'error': f'不支持手动触发任务 {job_id}'}
            
            return {'success': f'任务 {job_id} 执行完成'}
            
        except Exception as e:
            error_msg = f"手动触发任务 {job_id} 失败: {str(e)}"
            logger.error(error_msg)
            return {'error': error_msg}


# 全局调度器实例
_news_scheduler = None

def get_news_scheduler() -> NewsyncScheduler:
    """获取新闻同步调度器实例（单例模式）"""
    global _news_scheduler
    if _news_scheduler is None:
        _news_scheduler = NewsyncScheduler()
    return _news_scheduler


async def start_news_scheduler():
    """启动新闻同步调度器"""
    scheduler = get_news_scheduler()
    await scheduler.start()


async def stop_news_scheduler():
    """停止新闻同步调度器"""
    scheduler = get_news_scheduler()
    await scheduler.stop()


if __name__ == "__main__":
    # 测试代码
    async def test_scheduler():
        scheduler = NewsyncScheduler()
        
        # 测试手动同步
        await scheduler.manual_sync_all()
        
        # 查看状态
        status = scheduler.get_scheduler_status()
        print(f"调度器状态: {status}")
    
    # 运行测试
    asyncio.run(test_scheduler()) 