#!/usr/bin/env python3
"""
市场扫描器
扫描不同市场的MACD背离情况
支持按指数成分股扫描：纳斯达克100、恒生科技、中证300
"""

import asyncio
import time
from typing import Dict, List, Optional
import logging
from datetime import datetime, timedelta
import pandas as pd

from backend.divergence_detector import (
    get_divergence_detector, 
    get_divergence_database, 
    get_chart_generator,
    DivergenceSignal
)
from backend.data_manager import init_data_manager
from backend.index_components import get_index_stocks, get_all_index_names

logger = logging.getLogger(__name__)

class MarketScanner:
    """市场扫描器"""
    
    def __init__(self, tushare_token: str):
        self.tushare_token = tushare_token
        self.data_manager = init_data_manager(tushare_token)
        self.divergence_detector = get_divergence_detector()
        self.divergence_db = get_divergence_database()
        self.chart_generator = get_chart_generator()
        
        # 定义不同市场的股票池
        self.market_stocks = {
            'US': [
                'AAPL', 'MSFT', 'GOOGL', 'AMZN', 'TSLA', 'META', 'NVDA', 'NFLX',
                'ADBE', 'CRM', 'ORCL', 'INTC', 'AMD', 'QCOM', 'AVGO', 'TXN',
                'CSCO', 'IBM', 'UBER', 'LYFT', 'ZOOM', 'DOCU', 'SHOP', 'SQ',
                'PYPL', 'V', 'MA', 'JPM', 'BAC', 'WFC', 'GS', 'MS',
                'JNJ', 'PFE', 'UNH', 'ABBV', 'MRK', 'TMO', 'DHR', 'ABT',
                'KO', 'PEP', 'WMT', 'HD', 'MCD', 'NKE', 'SBUX', 'DIS'
            ],
            'HK': [
                '00700.HK', '00941.HK', '03690.HK', '00175.HK', '01024.HK',
                '02318.HK', '01299.HK', '00388.HK', '01398.HK', '03988.HK',
                '00939.HK', '01177.HK', '01093.HK', '01810.HK', '02020.HK',
                '01211.HK', '02382.HK', '01972.HK', '06618.HK', '01833.HK',
                '00883.HK', '02269.HK', '01918.HK', '01797.HK', '00868.HK',
                '01109.HK', '02331.HK', '01658.HK', '01896.HK', '00992.HK'
            ],
            'CN': [
                '000001.SZ', '000002.SZ', '000858.SZ', '000725.SZ', '000776.SZ',
                '600036.SH', '600519.SH', '600276.SH', '600887.SH', '600030.SH',
                '000858.SZ', '002415.SZ', '002594.SZ', '002230.SZ', '002304.SZ',
                '600104.SH', '600585.SH', '600690.SH', '600703.SH', '600745.SH',
                '300014.SZ', '300015.SZ', '300059.SZ', '300124.SZ', '300142.SZ',
                '688036.SH', '688111.SH', '688169.SH', '688187.SH', '688223.SH'
            ]
        }
    
    async def scan_index(self, index_name: str, divergence_types: List[str] = None) -> Dict:
        """
        扫描指定指数的背离情况
        
        Args:
            index_name: 指数名称 ('NASDAQ_100', 'HANG_SENG_TECH', 'CSI_300', 'ALL')
            divergence_types: 背离类型列表，默认 ['bullish', 'bearish']
            
        Returns:
            扫描结果字典
        """
        if index_name not in get_all_index_names():
            raise ValueError(f"不支持的指数: {index_name}，支持的指数: {get_all_index_names()}")
        
        if divergence_types is None:
            divergence_types = ['bullish', 'bearish']  # 默认扫描所有类型
        
        start_time = time.time()
        stock_list = get_index_stocks(index_name)
        
        logger.info(f"开始扫描{index_name}指数，共{len(stock_list)}只成分股")
        
        results = {
            'index': index_name,
            'scan_time': datetime.now().isoformat(),
            'total_stocks': len(stock_list),
            'scanned_stocks': 0,
            'divergences': [],
            'charts': {},
            'scan_duration': 0,
            'errors': []
        }
        
        # 并发扫描股票（限制并发数避免API限制）
        semaphore = asyncio.Semaphore(8)  # 增加并发数以提高速度
        tasks = []
        
        for symbol in stock_list:
            task = self._scan_single_stock(semaphore, symbol, index_name, divergence_types)
            tasks.append(task)
        
        # 等待所有任务完成
        scan_results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 处理结果
        for i, result in enumerate(scan_results):
            symbol = stock_list[i]
            
            if isinstance(result, Exception):
                error_msg = f"{symbol}: {str(result)}"
                results['errors'].append(error_msg)
                logger.error(f"扫描{symbol}失败: {result}")
                continue
            
            if result:
                divergences, chart_data = result
                results['scanned_stocks'] += 1
                
                for divergence in divergences:
                    # 生成图表
                    chart_image = None
                    if chart_data is not None:
                        try:
                            chart_image = self.chart_generator.generate_kline_chart(
                                chart_data, [divergence], symbol
                            )
                            results['charts'][symbol] = chart_image
                        except Exception as e:
                            logger.error(f"生成{symbol}图表失败: {e}")
                    
                    # 保存到数据库
                    try:
                        self.divergence_db.save_divergence_signal(divergence, index_name, chart_image)
                    except Exception as e:
                        logger.error(f"保存{symbol}背离信号失败: {e}")
                    
                    # 添加到结果
                    divergence_dict = {
                        'symbol': divergence.symbol,
                        'type': divergence.divergence_type.value,
                        'start_date': divergence.start_date,
                        'end_date': divergence.end_date,
                        'strength': divergence.strength,
                        'confidence': divergence.confidence,
                        'price_range': f"{divergence.price_low:.2f} - {divergence.price_high:.2f}",
                        'macd_range': f"{divergence.macd_low:.4f} - {divergence.macd_high:.4f}",
                        'detected_at': divergence.detected_at.isoformat()
                    }
                    results['divergences'].append(divergence_dict)
        
        # 按股票名称排序
        results['divergences'].sort(key=lambda x: x['symbol'])
        
        # 记录扫描时间
        scan_duration = time.time() - start_time
        results['scan_duration'] = scan_duration
        
        # 保存扫描历史
        try:
            self.divergence_db.save_scan_history(
                index_name, len(stock_list), len(results['divergences']), scan_duration
            )
        except Exception as e:
            logger.error(f"保存扫描历史失败: {e}")
        
        logger.info(f"{index_name}指数扫描完成，发现{len(results['divergences'])}个背离信号，耗时{scan_duration:.2f}秒")
        
        return results
    
    async def scan_market(self, market: str, divergence_types: List[str] = None) -> Dict:
        """扫描指定市场的背离情况"""
        if market not in self.market_stocks:
            raise ValueError(f"不支持的市场: {market}")
        
        if divergence_types is None:
            divergence_types = ['bullish', 'bearish']  # 默认扫描所有类型
        
        start_time = time.time()
        stock_list = self.market_stocks[market]
        
        logger.info(f"开始扫描{market}市场，共{len(stock_list)}只股票")
        
        results = {
            'market': market,
            'scan_time': datetime.now().isoformat(),
            'total_stocks': len(stock_list),
            'scanned_stocks': 0,
            'divergences': [],
            'charts': {},
            'scan_duration': 0,
            'errors': []
        }
        
        # 并发扫描股票（限制并发数避免API限制）
        semaphore = asyncio.Semaphore(5)  # 最多5个并发
        tasks = []
        
        for symbol in stock_list:
            task = self._scan_single_stock(semaphore, symbol, market, divergence_types)
            tasks.append(task)
        
        # 等待所有任务完成
        scan_results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 处理结果
        for i, result in enumerate(scan_results):
            symbol = stock_list[i]
            
            if isinstance(result, Exception):
                error_msg = f"{symbol}: {str(result)}"
                results['errors'].append(error_msg)
                logger.error(f"扫描{symbol}失败: {result}")
                continue
            
            if result:
                divergences, chart_data = result
                results['scanned_stocks'] += 1
                
                for divergence in divergences:
                    # 生成图表
                    chart_image = None
                    if chart_data is not None:
                        try:
                            chart_image = self.chart_generator.generate_kline_chart(
                                chart_data, [divergence], symbol
                            )
                            results['charts'][symbol] = chart_image
                        except Exception as e:
                            logger.error(f"生成{symbol}图表失败: {e}")
                    
                    # 保存到数据库
                    try:
                        self.divergence_db.save_divergence_signal(divergence, market, chart_image)
                    except Exception as e:
                        logger.error(f"保存{symbol}背离信号失败: {e}")
                    
                    # 添加到结果
                    divergence_dict = {
                        'symbol': divergence.symbol,
                        'type': divergence.divergence_type.value,
                        'start_date': divergence.start_date,
                        'end_date': divergence.end_date,
                        'strength': divergence.strength,
                        'confidence': divergence.confidence,
                        'price_range': f"{divergence.price_low:.2f} - {divergence.price_high:.2f}",
                        'macd_range': f"{divergence.macd_low:.4f} - {divergence.macd_high:.4f}",
                        'detected_at': divergence.detected_at.isoformat()
                    }
                    results['divergences'].append(divergence_dict)
        
        # 按股票名称排序
        results['divergences'].sort(key=lambda x: x['symbol'])
        
        # 记录扫描时间
        scan_duration = time.time() - start_time
        results['scan_duration'] = scan_duration
        
        # 保存扫描历史
        try:
            self.divergence_db.save_scan_history(
                market, len(stock_list), len(results['divergences']), scan_duration
            )
        except Exception as e:
            logger.error(f"保存扫描历史失败: {e}")
        
        logger.info(f"{market}市场扫描完成，发现{len(results['divergences'])}个背离信号，耗时{scan_duration:.2f}秒")
        
        return results
    
    async def _scan_single_stock(self, semaphore: asyncio.Semaphore, symbol: str, 
                                market_or_index: str, divergence_types: List[str]) -> Optional[tuple]:
        """扫描单只股票"""
        async with semaphore:
            try:
                # 获取股票数据
                stock_data = await self._get_stock_data_async(symbol)
                
                if stock_data is None or stock_data.empty:
                    return None
                
                # 确保有足够的数据
                if len(stock_data) < 100:
                    logger.warning(f"{symbol} 数据不足100天，跳过")
                    return None
                
                # 添加symbol信息到数据中
                stock_data['symbol'] = symbol
                
                # 检测背离
                divergences = self.divergence_detector.detect_divergence(stock_data)
                
                # 过滤指定类型的背离
                filtered_divergences = []
                for div in divergences:
                    if div.divergence_type.value in divergence_types:
                        div.symbol = symbol  # 确保symbol正确
                        filtered_divergences.append(div)
                
                if filtered_divergences:
                    logger.info(f"{symbol} 发现 {len(filtered_divergences)} 个背离信号")
                    return filtered_divergences, stock_data.tail(100)  # 返回最近100天数据用于绘图
                
                return None
                
            except Exception as e:
                logger.error(f"扫描{symbol}时出错: {e}")
                raise e
    
    async def _get_stock_data_async(self, symbol: str) -> Optional[pd.DataFrame]:
        """异步获取股票数据"""
        try:
            # 在线程池中执行同步操作
            loop = asyncio.get_event_loop()
            stock_data = await loop.run_in_executor(
                None, 
                self._get_stock_data_sync, 
                symbol
            )
            return stock_data
        except Exception as e:
            logger.error(f"获取{symbol}数据失败: {e}")
            return None
    
    def _get_stock_data_sync(self, symbol: str) -> Optional[pd.DataFrame]:
        """同步获取股票数据"""
        try:
            # 获取最近6个月的数据
            end_date = datetime.now().strftime('%Y%m%d')
            start_date = (datetime.now() - timedelta(days=180)).strftime('%Y%m%d')
            
            # 使用智能数据获取方法
            stock_data = self.data_manager.get_stock_data_intelligent(symbol, start_date, end_date)
            
            if stock_data.empty:
                logger.warning(f"无法获取{symbol}的数据")
                return None
            
            return stock_data
            
        except Exception as e:
            logger.error(f"获取{symbol}数据时出错: {e}")
            return None
    
    def get_recent_divergences(self, market: str = None, hours: int = 24) -> List[Dict]:
        """获取最近的背离信号"""
        return self.divergence_db.get_recent_divergences(market, hours)
    
    def get_scan_history(self, market: str = None, limit: int = 10) -> List[Dict]:
        """获取扫描历史"""
        query = "SELECT * FROM scan_history"
        params = []
        
        if market:
            query += " WHERE market = ?"
            params.append(market)
        
        query += " ORDER BY scan_date DESC LIMIT ?"
        params.append(limit)
        
        with self.divergence_db.get_connection() as conn:
            cursor = conn.execute(query, params)
            return [dict(row) for row in cursor.fetchall()]
    
    def get_supported_indexes(self) -> List[str]:
        """获取支持的指数列表"""
        return get_all_index_names()
    
    def get_index_stock_count(self, index_name: str) -> int:
        """获取指数成分股数量"""
        return len(get_index_stocks(index_name))

# 全局扫描器实例缓存
_scanner_cache = {}

def get_market_scanner(tushare_token: str) -> MarketScanner:
    """获取市场扫描器实例"""
    if tushare_token not in _scanner_cache:
        _scanner_cache[tushare_token] = MarketScanner(tushare_token)
    return _scanner_cache[tushare_token] 