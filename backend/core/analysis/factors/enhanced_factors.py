#!/usr/bin/env python3
"""
增强型多维度技术因子计算与管理模块
扩展现有因子计算功能，提供更全面的技术指标计算和因子管理能力
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Any, Optional, Tuple, Union
from datetime import datetime, timedelta
import logging
import warnings
from dataclasses import dataclass
from enum import Enum

# 导入现有的因子计算器
from .factors import FactorCalculator

logger = logging.getLogger(__name__)

class FactorCategory(Enum):
    """因子分类枚举"""
    TECHNICAL = "technical"  # 技术面因子
    MOMENTUM = "momentum"    # 动量因子
    VOLUME = "volume"        # 成交量因子
    VOLATILITY = "volatility" # 波动率因子
    TREND = "trend"          # 趋势因子
    REVERSAL = "reversal"    # 反转因子

@dataclass
class FactorConfig:
    """因子配置"""
    name: str
    category: FactorCategory
    description: str
    required_periods: int
    parameters: Dict[str, Any]
    enabled: bool = True

class EnhancedFactorCalculator(FactorCalculator):
    """增强型因子计算器"""
    
    def __init__(self):
        super().__init__()
        self.factor_configs = self._initialize_factor_configs()
        
    def _initialize_factor_configs(self) -> Dict[str, FactorConfig]:
        """初始化因子配置"""
        configs = {
            # 动量因子
            'rsi': FactorConfig('rsi', FactorCategory.MOMENTUM, 'RSI相对强弱指标', 14, {'period': 14}),
            'rsi_6': FactorConfig('rsi_6', FactorCategory.MOMENTUM, 'RSI(6)', 6, {'period': 6}),
            'rsi_24': FactorConfig('rsi_24', FactorCategory.MOMENTUM, 'RSI(24)', 24, {'period': 24}),
            
            # 趋势因子
            'macd': FactorConfig('macd', FactorCategory.TREND, 'MACD指标', 26, {'fast': 12, 'slow': 26, 'signal': 9}),
            'macd_signal': FactorConfig('macd_signal', FactorCategory.TREND, 'MACD信号线', 26, {'fast': 12, 'slow': 26, 'signal': 9}),
            'macd_histogram': FactorConfig('macd_histogram', FactorCategory.TREND, 'MACD柱状图', 26, {'fast': 12, 'slow': 26, 'signal': 9}),
            
            # 移动平均线
            'sma_5': FactorConfig('sma_5', FactorCategory.TREND, '5日简单移动平均', 5, {'period': 5}),
            'sma_10': FactorConfig('sma_10', FactorCategory.TREND, '10日简单移动平均', 10, {'period': 10}),
            'sma_20': FactorConfig('sma_20', FactorCategory.TREND, '20日简单移动平均', 20, {'period': 20}),
            'sma_50': FactorConfig('sma_50', FactorCategory.TREND, '50日简单移动平均', 50, {'period': 50}),
            'sma_200': FactorConfig('sma_200', FactorCategory.TREND, '200日简单移动平均', 200, {'period': 200}),
            
            # 指数移动平均线
            'ema_12': FactorConfig('ema_12', FactorCategory.TREND, '12日指数移动平均', 12, {'period': 12}),
            'ema_26': FactorConfig('ema_26', FactorCategory.TREND, '26日指数移动平均', 26, {'period': 26}),
            
            # 布林带
            'bollinger_upper': FactorConfig('bollinger_upper', FactorCategory.VOLATILITY, '布林带上轨', 20, {'period': 20, 'std': 2}),
            'bollinger_middle': FactorConfig('bollinger_middle', FactorCategory.VOLATILITY, '布林带中轨', 20, {'period': 20, 'std': 2}),
            'bollinger_lower': FactorConfig('bollinger_lower', FactorCategory.VOLATILITY, '布林带下轨', 20, {'period': 20, 'std': 2}),
            'bollinger_position': FactorConfig('bollinger_position', FactorCategory.VOLATILITY, '布林带位置', 20, {'period': 20, 'std': 2}),
            'bollinger_width': FactorConfig('bollinger_width', FactorCategory.VOLATILITY, '布林带宽度', 20, {'period': 20, 'std': 2}),
            
            # 成交量因子
            'volume_sma_20': FactorConfig('volume_sma_20', FactorCategory.VOLUME, '20日成交量均线', 20, {'period': 20}),
            'volume_ratio': FactorConfig('volume_ratio', FactorCategory.VOLUME, '量比', 20, {'period': 20}),
            'obv': FactorConfig('obv', FactorCategory.VOLUME, '能量潮指标', 1, {}),
            
            # 波动率因子
            'atr': FactorConfig('atr', FactorCategory.VOLATILITY, '平均真实波动率', 14, {'period': 14}),
            'price_volatility': FactorConfig('price_volatility', FactorCategory.VOLATILITY, '价格波动率', 20, {'period': 20}),
            
            # 随机指标
            'stoch_k': FactorConfig('stoch_k', FactorCategory.MOMENTUM, '随机指标K值', 14, {'k_period': 14, 'd_period': 3}),
            'stoch_d': FactorConfig('stoch_d', FactorCategory.MOMENTUM, '随机指标D值', 14, {'k_period': 14, 'd_period': 3}),
            
            # 威廉指标
            'williams_r': FactorConfig('williams_r', FactorCategory.MOMENTUM, '威廉指标', 14, {'period': 14}),
            
            # 商品通道指数
            'cci': FactorConfig('cci', FactorCategory.MOMENTUM, '商品通道指数', 20, {'period': 20}),
            
            # 动量指标
            'momentum': FactorConfig('momentum', FactorCategory.MOMENTUM, '动量指标', 10, {'period': 10}),
            'roc': FactorConfig('roc', FactorCategory.MOMENTUM, '变动率指标', 12, {'period': 12}),
        }
        return configs
    
    def calculate_enhanced_macd(self, prices: np.ndarray, fast: int = 12, slow: int = 26, signal: int = 9) -> Dict[str, float]:
        """计算增强版MACD指标"""
        if len(prices) < slow:
            return {'macd': 0.0, 'signal': 0.0, 'histogram': 0.0}
        
        try:
            # 计算指数移动平均
            ema_fast = pd.Series(prices).ewm(span=fast).mean()
            ema_slow = pd.Series(prices).ewm(span=slow).mean()
            
            # MACD线
            macd_line = ema_fast - ema_slow
            
            # 信号线
            signal_line = macd_line.ewm(span=signal).mean()
            
            # 柱状图
            histogram = macd_line - signal_line
            
            return {
                'macd': float(macd_line.iloc[-1]),
                'signal': float(signal_line.iloc[-1]),
                'histogram': float(histogram.iloc[-1])
            }
        except Exception as e:
            logger.error(f"计算MACD时出错: {e}")
            return {'macd': 0.0, 'signal': 0.0, 'histogram': 0.0}
    
    def calculate_ema(self, prices: np.ndarray, period: int) -> float:
        """计算指数移动平均"""
        if len(prices) < period:
            return float(prices[-1]) if len(prices) > 0 else 0.0
        
        try:
            ema = pd.Series(prices).ewm(span=period).mean().iloc[-1]
            return float(ema)
        except Exception as e:
            logger.error(f"计算EMA时出错: {e}")
            return float(prices[-1]) if len(prices) > 0 else 0.0
    
    def calculate_enhanced_bollinger_bands(self, prices: np.ndarray, period: int = 20, std_dev: int = 2) -> Dict[str, float]:
        """计算增强版布林带"""
        if len(prices) < period:
            price = float(prices[-1]) if len(prices) > 0 else 0.0
            return {
                'upper': price,
                'middle': price,
                'lower': price,
                'position': 0.5,
                'width': 0.0
            }
        
        try:
            recent_prices = prices[-period:]
            middle = np.mean(recent_prices)
            std = np.std(recent_prices, ddof=1)
            upper = middle + (std * std_dev)
            lower = middle - (std * std_dev)
            
            current_price = prices[-1]
            position = (current_price - lower) / (upper - lower) if upper != lower else 0.5
            width = (upper - lower) / middle if middle != 0 else 0.0
            
            return {
                'upper': float(upper),
                'middle': float(middle),
                'lower': float(lower),
                'position': float(position),
                'width': float(width)
            }
        except Exception as e:
            logger.error(f"计算布林带时出错: {e}")
            price = float(prices[-1]) if len(prices) > 0 else 0.0
            return {
                'upper': price,
                'middle': price,
                'lower': price,
                'position': 0.5,
                'width': 0.0
            }
    
    def calculate_stochastic(self, high: np.ndarray, low: np.ndarray, close: np.ndarray, 
                           k_period: int = 14, d_period: int = 3) -> Dict[str, float]:
        """计算随机指标"""
        if len(high) < k_period or len(low) < k_period or len(close) < k_period:
            return {'k': 50.0, 'd': 50.0}
        
        try:
            # 计算%K
            lowest_low = pd.Series(low).rolling(window=k_period).min()
            highest_high = pd.Series(high).rolling(window=k_period).max()
            
            k_percent = 100 * (pd.Series(close) - lowest_low) / (highest_high - lowest_low)
            k_percent = k_percent.fillna(50.0)
            
            # 计算%D（%K的移动平均）
            d_percent = k_percent.rolling(window=d_period).mean()
            d_percent = d_percent.fillna(50.0)
            
            return {
                'k': float(k_percent.iloc[-1]),
                'd': float(d_percent.iloc[-1])
            }
        except Exception as e:
            logger.error(f"计算随机指标时出错: {e}")
            return {'k': 50.0, 'd': 50.0}
    
    def calculate_williams_r(self, high: np.ndarray, low: np.ndarray, close: np.ndarray, period: int = 14) -> float:
        """计算威廉指标"""
        if len(high) < period or len(low) < period or len(close) < period:
            return -50.0
        
        try:
            recent_high = np.max(high[-period:])
            recent_low = np.min(low[-period:])
            
            if recent_high == recent_low:
                return -50.0
            
            williams_r = -100 * (recent_high - close[-1]) / (recent_high - recent_low)
            return float(williams_r)
        except Exception as e:
            logger.error(f"计算威廉指标时出错: {e}")
            return -50.0
    
    def calculate_cci(self, high: np.ndarray, low: np.ndarray, close: np.ndarray, period: int = 20) -> float:
        """计算商品通道指数"""
        if len(high) < period or len(low) < period or len(close) < period:
            return 0.0
        
        try:
            # 典型价格
            typical_price = (high + low + close) / 3
            
            # 移动平均
            sma_tp = pd.Series(typical_price).rolling(window=period).mean()
            
            # 平均绝对偏差
            mad = pd.Series(typical_price).rolling(window=period).apply(
                lambda x: np.mean(np.abs(x - x.mean()))
            )
            
            # CCI计算
            cci = (typical_price[-1] - sma_tp.iloc[-1]) / (0.015 * mad.iloc[-1])
            
            return float(cci) if not np.isnan(cci) else 0.0
        except Exception as e:
            logger.error(f"计算CCI时出错: {e}")
            return 0.0
    
    def calculate_obv(self, close: np.ndarray, volume: np.ndarray) -> float:
        """计算能量潮指标"""
        if len(close) < 2 or len(volume) < 2:
            return 0.0
        
        try:
            obv = 0
            for i in range(1, len(close)):
                if close[i] > close[i-1]:
                    obv += volume[i]
                elif close[i] < close[i-1]:
                    obv -= volume[i]
            
            return float(obv)
        except Exception as e:
            logger.error(f"计算OBV时出错: {e}")
            return 0.0
    
    def calculate_momentum(self, prices: np.ndarray, period: int = 10) -> float:
        """计算动量指标"""
        if len(prices) < period + 1:
            return 0.0
        
        try:
            momentum = prices[-1] - prices[-period-1]
            return float(momentum)
        except Exception as e:
            logger.error(f"计算动量指标时出错: {e}")
            return 0.0
    
    def calculate_roc(self, prices: np.ndarray, period: int = 12) -> float:
        """计算变动率指标"""
        if len(prices) < period + 1:
            return 0.0
        
        try:
            if prices[-period-1] == 0:
                return 0.0
            
            roc = ((prices[-1] - prices[-period-1]) / prices[-period-1]) * 100
            return float(roc)
        except Exception as e:
            logger.error(f"计算ROC时出错: {e}")
            return 0.0
    
    def calculate_price_volatility(self, prices: np.ndarray, period: int = 20) -> float:
        """计算价格波动率"""
        if len(prices) < period:
            return 0.0
        
        try:
            returns = np.diff(prices[-period:]) / prices[-period:-1]
            volatility = np.std(returns) * np.sqrt(252)  # 年化波动率
            return float(volatility)
        except Exception as e:
            logger.error(f"计算价格波动率时出错: {e}")
            return 0.0
    
    def calculate_volume_ratio(self, volume: np.ndarray, period: int = 20) -> float:
        """计算量比"""
        if len(volume) < period:
            return 1.0
        
        try:
            avg_volume = np.mean(volume[-period:-1])  # 排除当日
            current_volume = volume[-1]
            
            if avg_volume == 0:
                return 1.0
            
            ratio = current_volume / avg_volume
            return float(ratio)
        except Exception as e:
            logger.error(f"计算量比时出错: {e}")
            return 1.0
    
    def calculate_all_enhanced_factors(self, data: pd.DataFrame) -> Dict[str, float]:
        """计算所有增强型因子"""
        factors = {}
        
        # 确保列名标准化
        close = data['close'].values if 'close' in data.columns else None
        high = data['high'].values if 'high' in data.columns else None
        low = data['low'].values if 'low' in data.columns else None
        volume = data['volume'].values if 'volume' in data.columns else None
        
        if close is None or len(close) == 0:
            logger.warning("数据中缺少 'close' 列或数据为空")
            return {}
        
        try:
            # RSI系列
            factors['rsi'] = self.calculate_rsi(close, 14)
            factors['rsi_6'] = self.calculate_rsi(close, 6)
            factors['rsi_24'] = self.calculate_rsi(close, 24)
            
            # MACD系列
            macd_results = self.calculate_enhanced_macd(close)
            factors.update({
                'macd': macd_results['macd'],
                'macd_signal': macd_results['signal'],
                'macd_histogram': macd_results['histogram']
            })
            
            # 移动平均线系列
            factors['sma_5'] = self.calculate_sma(close, 5)
            factors['sma_10'] = self.calculate_sma(close, 10)
            factors['sma_20'] = self.calculate_sma(close, 20)
            factors['sma_50'] = self.calculate_sma(close, 50)
            factors['sma_200'] = self.calculate_sma(close, 200)
            
            # 指数移动平均线
            factors['ema_12'] = self.calculate_ema(close, 12)
            factors['ema_26'] = self.calculate_ema(close, 26)
            
            # 布林带系列
            bollinger_results = self.calculate_enhanced_bollinger_bands(close)
            factors.update({
                'bollinger_upper': bollinger_results['upper'],
                'bollinger_middle': bollinger_results['middle'],
                'bollinger_lower': bollinger_results['lower'],
                'bollinger_position': bollinger_results['position'],
                'bollinger_width': bollinger_results['width']
            })
            
            # 需要高低价的指标
            if high is not None and low is not None:
                # 随机指标
                stoch_results = self.calculate_stochastic(high, low, close)
                factors.update({
                    'stoch_k': stoch_results['k'],
                    'stoch_d': stoch_results['d']
                })
                
                # 威廉指标
                factors['williams_r'] = self.calculate_williams_r(high, low, close)
                
                # CCI指标
                factors['cci'] = self.calculate_cci(high, low, close)
                
                # ATR
                factors['atr'] = self.calculate_atr_enhanced(high, low, close)
            
            # 成交量相关指标
            if volume is not None and len(volume) > 0:
                factors['volume_sma_20'] = self.calculate_sma(volume, 20)
                factors['volume_ratio'] = self.calculate_volume_ratio(volume)
                factors['obv'] = self.calculate_obv(close, volume)
            
            # 其他技术指标
            factors['momentum'] = self.calculate_momentum(close)
            factors['roc'] = self.calculate_roc(close)
            factors['price_volatility'] = self.calculate_price_volatility(close)
            
        except Exception as e:
            logger.error(f"计算增强型因子时出错: {e}")
        
        return factors
    
    def calculate_atr_enhanced(self, high: np.ndarray, low: np.ndarray, close: np.ndarray, period: int = 14) -> float:
        """计算增强版ATR"""
        if len(high) < 2 or len(low) < 2 or len(close) < 2:
            return 0.0
        
        try:
            tr_list = []
            for i in range(1, min(period + 1, len(high))):
                tr1 = high[i] - low[i]
                tr2 = abs(high[i] - close[i-1])
                tr3 = abs(low[i] - close[i-1])
                tr_list.append(max(tr1, tr2, tr3))
            
            if tr_list:
                return float(np.mean(tr_list))
            else:
                return 0.0
        except Exception as e:
            logger.error(f"计算ATR时出错: {e}")
            return 0.0
    
    def get_factor_categories(self) -> Dict[str, List[str]]:
        """获取按分类组织的因子列表"""
        categories = {}
        for factor_name, config in self.factor_configs.items():
            category = config.category.value
            if category not in categories:
                categories[category] = []
            categories[category].append(factor_name)
        return categories
    
    def get_enabled_factors(self) -> List[str]:
        """获取已启用的因子列表"""
        return [name for name, config in self.factor_configs.items() if config.enabled]
    
    def get_factor_info(self, factor_name: str) -> Optional[FactorConfig]:
        """获取因子信息"""
        return self.factor_configs.get(factor_name)
    
    def set_factor_enabled(self, factor_name: str, enabled: bool):
        """设置因子是否启用"""
        if factor_name in self.factor_configs:
            self.factor_configs[factor_name].enabled = enabled
        else:
            logger.warning(f"未知因子: {factor_name}")
    
    def calculate_selected_factors(self, data: pd.DataFrame, factor_names: List[str]) -> Dict[str, float]:
        """计算指定的因子"""
        all_factors = self.calculate_all_enhanced_factors(data)
        return {name: all_factors.get(name, 0.0) for name in factor_names if name in all_factors}

class FactorManager:
    """因子管理器"""
    
    def __init__(self, data_manager=None):
        self.calculator = EnhancedFactorCalculator()
        self.data_manager = data_manager
        self.cache = {}
        
    def calculate_factors_for_stock(self, symbol: str, factor_names: List[str] = None, 
                                  start_date: str = None, end_date: str = None) -> Dict[str, float]:
        """为单只股票计算因子"""
        if self.data_manager is None:
            logger.error("数据管理器未初始化")
            return {}
        
        try:
            # 获取股票数据
            data = self.data_manager.get_stock_data(symbol, start_date, end_date)
            
            if data.empty:
                logger.warning(f"未获取到股票 {symbol} 的数据")
                return {}
            
            # 计算因子
            if factor_names is None:
                factors = self.calculator.calculate_all_enhanced_factors(data)
            else:
                factors = self.calculator.calculate_selected_factors(data, factor_names)
            
            return factors
            
        except Exception as e:
            logger.error(f"计算股票 {symbol} 因子时出错: {e}")
            return {}
    
    def batch_calculate_factors(self, symbols: List[str], factor_names: List[str] = None, 
                              start_date: str = None, end_date: str = None) -> Dict[str, Dict[str, float]]:
        """批量计算多只股票的因子"""
        results = {}
        
        for symbol in symbols:
            try:
                factors = self.calculate_factors_for_stock(symbol, factor_names, start_date, end_date)
                if factors:
                    results[symbol] = factors
                    logger.info(f"已计算股票 {symbol} 的因子")
            except Exception as e:
                logger.error(f"计算股票 {symbol} 因子失败: {e}")
                
        return results
    
    def get_factor_summary(self) -> Dict[str, Any]:
        """获取因子汇总信息"""
        categories = self.calculator.get_factor_categories()
        enabled_factors = self.calculator.get_enabled_factors()
        
        return {
            'total_factors': len(self.calculator.factor_configs),
            'enabled_factors': len(enabled_factors),
            'categories': {
                category: {
                    'count': len(factors),
                    'factors': factors
                } for category, factors in categories.items()
            },
            'enabled_factors_list': enabled_factors
        }
    
    def validate_factor_data(self, data: pd.DataFrame) -> bool:
        """验证数据是否适合因子计算"""
        required_columns = ['close']
        optional_columns = ['high', 'low', 'volume', 'open']
        
        # 检查必需列
        for col in required_columns:
            if col not in data.columns:
                logger.error(f"缺少必需列: {col}")
                return False
        
        # 检查数据长度
        if len(data) < 1:
            logger.error("数据为空")
            return False
        
        # 检查数据质量
        close_data = data['close'].values
        if np.all(np.isnan(close_data)) or np.all(close_data == 0):
            logger.error("收盘价数据无效")
            return False
        
        return True

# 工具函数
def get_enhanced_factor_calculator() -> EnhancedFactorCalculator:
    """获取增强型因子计算器实例"""
    return EnhancedFactorCalculator()

def get_factor_manager(data_manager=None) -> FactorManager:
    """获取因子管理器实例"""
    return FactorManager(data_manager)

def list_all_available_factors() -> Dict[str, List[str]]:
    """列出所有可用的因子"""
    calculator = EnhancedFactorCalculator()
    return calculator.get_factor_categories()

def get_factor_descriptions() -> Dict[str, str]:
    """获取所有因子的描述"""
    calculator = EnhancedFactorCalculator()
    return {name: config.description for name, config in calculator.factor_configs.items()} 