# Copyright (c) 2025 Bytedance Ltd. and/or its affiliates
# SPDX-License-Identifier: MIT

import json
import logging
import time
from typing import Annotated, Optional, List, Dict, Any
from datetime import datetime, timedelta
import akshare as ak
import pandas as pd
from concurrent.futures import ThreadPoolExecutor, as_completed

from langchain_core.tools import tool
from ..decorators import log_io
from ..cache_decorator import news_cached
from ..cache_keys import news_key
from backend.financial_news_manager import get_financial_news_manager

logger = logging.getLogger(__name__)

# 数据源配置
AKSHARE_NEWS_SOURCES = {
    'eastmoney_breakfast': {
        'name': '东方财富财经早餐',
        'func': ak.stock_info_cjzc_em,
        'args': [],
        'kwargs': {},
        'cache_ttl': 1800,  # 30分钟
        'description': '每日财经要闻汇总'
    },
    'eastmoney_global': {
        'name': '东方财富全球财经快讯',
        'func': ak.stock_info_global_em,
        'args': [],
        'kwargs': {},
        'cache_ttl': 300,   # 5分钟
        'description': '全球财经实时快讯'
    },
    'sina_global': {
        'name': '新浪财经全球财经快讯',
        'func': ak.stock_info_global_sina,
        'args': [],
        'kwargs': {},
        'cache_ttl': 300,   # 5分钟
        'description': '新浪财经实时快讯'
    },
    'futu_global': {
        'name': '富途牛牛快讯',
        'func': ak.stock_info_global_futu,
        'args': [],
        'kwargs': {},
        'cache_ttl': 300,   # 5分钟
        'description': '富途牛牛财经快讯'
    },
    'ths_global': {
        'name': '同花顺财经全球财经直播',
        'func': ak.stock_info_global_ths,
        'args': [],
        'kwargs': {},
        'cache_ttl': 300,   # 5分钟
        'description': '同花顺财经直播'
    },
    'cls_global': {
        'name': '财联社电报',
        'func': ak.stock_info_global_cls,
        'args': [],
        'kwargs': {'symbol': '全部'},
        'cache_ttl': 300,   # 5分钟
        'description': '财联社电报快讯'
    },
    'sina_broker': {
        'name': '新浪财经证券原创',
        'func': ak.stock_info_broker_sina,
        'args': [],
        'kwargs': {'page': '1'},
        'cache_ttl': 900,   # 15分钟
        'description': '新浪财经证券原创文章'
    }
}


def standardize_news_format(data: pd.DataFrame, source: str, source_name: str) -> List[Dict[str, Any]]:
    """
    统一新闻数据格式
    
    Args:
        data: 原始数据DataFrame
        source: 数据源标识
        source_name: 数据源显示名称
        
    Returns:
        标准化的新闻数据列表
    """
    if data.empty:
        return []
    
    standardized_news = []
    
    try:
        for _, row in data.iterrows():
            news_item = {}
            
            # 根据不同数据源的字段映射进行标准化
            if source == 'eastmoney_breakfast':
                news_item = {
                    'title': str(row.get('标题', '')),
                    'content': str(row.get('摘要', ''))[:500],  # 限制长度
                    'publish_time': str(row.get('发布时间', '')),
                    'url': str(row.get('链接', '')),
                    'category': '财经早餐'
                }
            elif source == 'eastmoney_global':
                news_item = {
                    'title': str(row.get('标题', '')),
                    'content': str(row.get('摘要', ''))[:500],
                    'publish_time': str(row.get('发布时间', '')),
                    'url': str(row.get('链接', '')),
                    'category': '全球财经快讯'
                }
            elif source == 'sina_global':
                news_item = {
                    'title': str(row.get('内容', ''))[:100],  # 新浪的内容作为标题
                    'content': str(row.get('内容', ''))[:500],
                    'publish_time': str(row.get('时间', '')),
                    'url': '',  # 新浪全球快讯没有链接
                    'category': '新浪财经快讯'
                }
            elif source == 'futu_global':
                news_item = {
                    'title': str(row.get('标题', '')),
                    'content': str(row.get('内容', ''))[:500],
                    'publish_time': str(row.get('发布时间', '')),
                    'url': str(row.get('链接', '')),
                    'category': '富途牛牛快讯'
                }
            elif source == 'ths_global':
                news_item = {
                    'title': str(row.get('标题', '')),
                    'content': str(row.get('内容', ''))[:500],
                    'publish_time': str(row.get('发布时间', '')),
                    'url': str(row.get('链接', '')),
                    'category': '同花顺财经直播'
                }
            elif source == 'cls_global':
                # 财联社有两个时间字段，合并处理
                publish_date = str(row.get('发布日期', ''))
                publish_time = str(row.get('发布时间', ''))
                full_time = f"{publish_date} {publish_time}" if publish_date and publish_time else publish_date or publish_time
                
                news_item = {
                    'title': str(row.get('标题', '')),
                    'content': str(row.get('内容', ''))[:500],
                    'publish_time': full_time,
                    'url': '',  # 财联社电报没有链接
                    'category': '财联社电报'
                }
            elif source == 'sina_broker':
                news_item = {
                    'title': str(row.get('内容', ''))[:100],  # 内容作为标题
                    'content': str(row.get('内容', ''))[:500],
                    'publish_time': str(row.get('时间', '')),
                    'url': str(row.get('链接', '')),
                    'category': '新浪证券原创'
                }
            
            # 验证必要字段
            if news_item.get('title') and news_item.get('publish_time'):
                # 清理和验证时间格式
                try:
                    # 尝试解析时间格式
                    if news_item['publish_time']:
                        # 处理不同的时间格式
                        time_str = news_item['publish_time'].strip()
                        if len(time_str) == 10:  # 只有日期
                            time_str += ' 00:00:00'
                        elif len(time_str) == 16:  # 缺少秒
                            time_str += ':00'
                        
                        # 验证时间格式
                        datetime.strptime(time_str, '%Y-%m-%d %H:%M:%S')
                        news_item['publish_time'] = time_str
                    
                    standardized_news.append(news_item)
                    
                except ValueError as e:
                    logger.warning(f"时间格式错误，跳过新闻: {news_item.get('title', '')[:50]}, 时间: {news_item.get('publish_time', '')}, 错误: {e}")
                    continue
            else:
                logger.warning(f"新闻数据不完整，跳过: {news_item}")
                
    except Exception as e:
        logger.error(f"标准化新闻格式失败 [{source}]: {e}")
    
    return standardized_news


def fetch_single_source_news(source: str, config: Dict[str, Any]) -> Dict[str, Any]:
    """
    获取单个数据源的新闻
    
    Args:
        source: 数据源标识
        config: 数据源配置
        
    Returns:
        包含新闻数据和状态信息的字典
    """
    result = {
        'source': source,
        'source_name': config['name'],
        'status': 'error',
        'news_data': [],
        'count': 0,
        'error_message': None,
        'response_time': 0
    }
    
    start_time = time.time()
    
    try:
        logger.info(f"开始获取 {config['name']} 新闻数据...")
        
        # 调用akshare接口
        data = config['func'](*config['args'], **config['kwargs'])
        
        end_time = time.time()
        result['response_time'] = end_time - start_time
        
        if isinstance(data, pd.DataFrame) and not data.empty:
            # 标准化数据格式
            standardized_news = standardize_news_format(data, source, config['name'])
            
            if standardized_news:
                result['status'] = 'success'
                result['news_data'] = standardized_news
                result['count'] = len(standardized_news)
                logger.info(f"{config['name']} 获取成功: {len(standardized_news)} 条新闻")
            else:
                result['error_message'] = "数据格式化后为空"
                logger.warning(f"{config['name']} 数据格式化后为空")
        else:
            result['error_message'] = "返回数据为空或格式错误"
            logger.warning(f"{config['name']} 返回数据为空")
            
    except Exception as e:
        end_time = time.time()
        result['response_time'] = end_time - start_time
        result['error_message'] = str(e)
        logger.error(f"{config['name']} 获取失败: {e}")
    
    return result


@tool
@log_io
def akshare_financial_breakfast_tool() -> str:
    """
    【AkShare工具】获取东方财富财经早餐数据并保存到数据库。
    
    专门获取东方财富财经早餐的每日财经要闻汇总，数据会自动保存到本地数据库。
    
    Returns:
        JSON格式的财经早餐数据和保存状态
    """
    try:
        # 获取数据库管理器
        news_manager = get_financial_news_manager()
        
        # 获取财经早餐数据
        source = 'eastmoney_breakfast'
        config = AKSHARE_NEWS_SOURCES[source]
        
        result = fetch_single_source_news(source, config)
        
        # 保存到数据库
        if result['status'] == 'success' and result['news_data']:
            inserted, updated = news_manager.save_news_batch(
                result['news_data'], 
                source, 
                config['name']
            )
            
            # 更新数据源状态
            news_manager.update_source_status(
                source, 
                'active', 
                None, 
                result['response_time']
            )
            
            return json.dumps({
                'source': config['name'],
                'status': 'success',
                'news_count': result['count'],
                'database_result': {
                    'inserted': inserted,
                    'updated': updated
                },
                'response_time': result['response_time'],
                'timestamp': datetime.now().isoformat()
            }, ensure_ascii=False, indent=2)
        else:
            # 更新数据源状态为错误
            news_manager.update_source_status(
                source, 
                'error', 
                result['error_message'], 
                result['response_time']
            )
            
            return json.dumps({
                'source': config['name'],
                'status': 'error',
                'error_message': result['error_message'],
                'response_time': result['response_time'],
                'timestamp': datetime.now().isoformat()
            }, ensure_ascii=False, indent=2)
            
    except Exception as e:
        error_msg = f"获取财经早餐失败: {str(e)}"
        logger.error(error_msg)
        return json.dumps({
            'error': error_msg,
            'timestamp': datetime.now().isoformat()
        }, ensure_ascii=False)


@tool
@log_io
def akshare_financial_global_news_tool(
    sources: Annotated[str, "数据源列表，用逗号分隔，如'eastmoney_global,sina_global'。留空获取所有全球快讯源"] = "",
    max_workers: Annotated[int, "并发线程数，默认3"] = 3
) -> str:
    """
    【AkShare工具】获取多源全球财经快讯数据并保存到数据库。
    
    并发获取多个数据源的全球财经快讯，包括东方财富、新浪、富途、同花顺、财联社等。
    数据会自动保存到本地数据库。
    
    Args:
        sources: 指定数据源，支持eastmoney_global,sina_global,futu_global,ths_global,cls_global
        max_workers: 并发线程数
        
    Returns:
        JSON格式的多源财经快讯数据和保存状态
    """
    try:
        # 获取数据库管理器
        news_manager = get_financial_news_manager()
        
        # 确定要获取的数据源
        global_sources = ['eastmoney_global', 'sina_global', 'futu_global', 'ths_global', 'cls_global']
        
        if sources.strip():
            source_list = [s.strip() for s in sources.split(',') if s.strip()]
            source_list = [s for s in source_list if s in global_sources]
        else:
            source_list = global_sources
        
        if not source_list:
            return json.dumps({
                'error': '无效的数据源',
                'supported_sources': global_sources,
                'timestamp': datetime.now().isoformat()
            }, ensure_ascii=False)
        
        logger.info(f"开始并发获取全球财经快讯: {source_list}")
        
        # 并发获取多个数据源
        results = []
        total_inserted = 0
        total_updated = 0
        
        with ThreadPoolExecutor(max_workers=min(max_workers, len(source_list))) as executor:
            # 提交所有任务
            future_to_source = {
                executor.submit(fetch_single_source_news, source, AKSHARE_NEWS_SOURCES[source]): source
                for source in source_list
            }
            
            # 收集结果
            for future in as_completed(future_to_source):
                source = future_to_source[future]
                try:
                    result = future.result()
                    
                    # 保存到数据库
                    if result['status'] == 'success' and result['news_data']:
                        inserted, updated = news_manager.save_news_batch(
                            result['news_data'], 
                            source, 
                            result['source_name']
                        )
                        
                        result['database_result'] = {
                            'inserted': inserted,
                            'updated': updated
                        }
                        
                        total_inserted += inserted
                        total_updated += updated
                        
                        # 更新数据源状态
                        news_manager.update_source_status(
                            source, 
                            'active', 
                            None, 
                            result['response_time']
                        )
                    else:
                        # 更新数据源状态为错误
                        news_manager.update_source_status(
                            source, 
                            'error', 
                            result['error_message'], 
                            result['response_time']
                        )
                    
                    results.append(result)
                    
                except Exception as e:
                    logger.error(f"处理数据源 {source} 结果失败: {e}")
                    results.append({
                        'source': source,
                        'status': 'error',
                        'error_message': str(e)
                    })
        
        # 统计结果
        success_count = sum(1 for r in results if r['status'] == 'success')
        total_news = sum(r.get('count', 0) for r in results)
        
        return json.dumps({
            'status': 'completed',
            'total_sources': len(source_list),
            'success_sources': success_count,
            'total_news': total_news,
            'database_summary': {
                'total_inserted': total_inserted,
                'total_updated': total_updated
            },
            'source_results': results,
            'timestamp': datetime.now().isoformat()
        }, ensure_ascii=False, indent=2)
        
    except Exception as e:
        error_msg = f"获取全球财经快讯失败: {str(e)}"
        logger.error(error_msg)
        return json.dumps({
            'error': error_msg,
            'timestamp': datetime.now().isoformat()
        }, ensure_ascii=False)


@tool
@log_io
def akshare_financial_comprehensive_tool(
    include_breakfast: Annotated[bool, "是否包含财经早餐，默认True"] = True,
    include_global: Annotated[bool, "是否包含全球快讯，默认True"] = True,
    include_broker: Annotated[bool, "是否包含证券原创，默认False"] = False,
    max_workers: Annotated[int, "并发线程数，默认5"] = 5
) -> str:
    """
    【AkShare工具】获取综合财经资讯数据并保存到数据库。
    
    一次性获取所有或指定类型的财经资讯数据，包括财经早餐、全球快讯、证券原创等。
    数据会自动保存到本地数据库。
    
    Args:
        include_breakfast: 是否包含财经早餐
        include_global: 是否包含全球快讯
        include_broker: 是否包含证券原创
        max_workers: 并发线程数
        
    Returns:
        JSON格式的综合财经资讯数据和保存状态
    """
    try:
        # 获取数据库管理器
        news_manager = get_financial_news_manager()
        
        # 确定要获取的数据源
        source_list = []
        
        if include_breakfast:
            source_list.append('eastmoney_breakfast')
        
        if include_global:
            source_list.extend(['eastmoney_global', 'sina_global', 'futu_global', 'ths_global', 'cls_global'])
        
        if include_broker:
            source_list.append('sina_broker')
        
        if not source_list:
            return json.dumps({
                'error': '未选择任何数据源',
                'timestamp': datetime.now().isoformat()
            }, ensure_ascii=False)
        
        logger.info(f"开始并发获取综合财经资讯: {source_list}")
        
        # 并发获取多个数据源
        results = []
        total_inserted = 0
        total_updated = 0
        
        with ThreadPoolExecutor(max_workers=min(max_workers, len(source_list))) as executor:
            # 提交所有任务
            future_to_source = {
                executor.submit(fetch_single_source_news, source, AKSHARE_NEWS_SOURCES[source]): source
                for source in source_list
            }
            
            # 收集结果
            for future in as_completed(future_to_source):
                source = future_to_source[future]
                try:
                    result = future.result()
                    
                    # 保存到数据库
                    if result['status'] == 'success' and result['news_data']:
                        inserted, updated = news_manager.save_news_batch(
                            result['news_data'], 
                            source, 
                            result['source_name']
                        )
                        
                        result['database_result'] = {
                            'inserted': inserted,
                            'updated': updated
                        }
                        
                        total_inserted += inserted
                        total_updated += updated
                        
                        # 更新数据源状态
                        news_manager.update_source_status(
                            source, 
                            'active', 
                            None, 
                            result['response_time']
                        )
                    else:
                        # 更新数据源状态为错误
                        news_manager.update_source_status(
                            source, 
                            'error', 
                            result['error_message'], 
                            result['response_time']
                        )
                    
                    results.append(result)
                    
                except Exception as e:
                    logger.error(f"处理数据源 {source} 结果失败: {e}")
                    results.append({
                        'source': source,
                        'status': 'error',
                        'error_message': str(e)
                    })
        
        # 统计结果
        success_count = sum(1 for r in results if r['status'] == 'success')
        total_news = sum(r.get('count', 0) for r in results)
        
        # 按类型分组统计
        breakfast_results = [r for r in results if r['source'] == 'eastmoney_breakfast']
        global_results = [r for r in results if r['source'] in ['eastmoney_global', 'sina_global', 'futu_global', 'ths_global', 'cls_global']]
        broker_results = [r for r in results if r['source'] == 'sina_broker']
        
        return json.dumps({
            'status': 'completed',
            'total_sources': len(source_list),
            'success_sources': success_count,
            'total_news': total_news,
            'database_summary': {
                'total_inserted': total_inserted,
                'total_updated': total_updated
            },
            'category_summary': {
                'breakfast': {
                    'count': sum(r.get('count', 0) for r in breakfast_results),
                    'sources': len(breakfast_results)
                },
                'global_news': {
                    'count': sum(r.get('count', 0) for r in global_results),
                    'sources': len(global_results)
                },
                'broker_news': {
                    'count': sum(r.get('count', 0) for r in broker_results),
                    'sources': len(broker_results)
                }
            },
            'source_results': results,
            'timestamp': datetime.now().isoformat()
        }, ensure_ascii=False, indent=2)
        
    except Exception as e:
        error_msg = f"获取综合财经资讯失败: {str(e)}"
        logger.error(error_msg)
        return json.dumps({
            'error': error_msg,
            'timestamp': datetime.now().isoformat()
        }, ensure_ascii=False)


if __name__ == "__main__":
    # 测试代码
    print("=== 测试财经早餐工具 ===")
    result = akshare_financial_breakfast_tool.invoke({})
    print(result)
    
    print("\n=== 测试全球财经快讯工具 ===")
    result = akshare_financial_global_news_tool.invoke({"sources": "eastmoney_global,sina_global"})
    print(result) 