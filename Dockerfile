# ================================
# 阶段1: 前端构建
# ================================
FROM node:18-alpine AS frontend-builder

WORKDIR /frontend

# 复制前端依赖文件
COPY frontend/package*.json ./

# 安装前端依赖
RUN npm ci --only=production

# 复制前端源代码
COPY frontend/ .

# 构建前端应用
RUN npm run build

# ================================
# 阶段2: 后端和服务配置
# ================================
FROM python:3.11-slim AS production

# 设置环境变量
ENV PYTHONDONTWRITEBYTECODE=1
ENV PYTHONUNBUFFERED=1
ENV DEBIAN_FRONTEND=noninteractive

# 创建应用用户
RUN groupadd -r app && useradd -r -g app app

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    nginx \
    supervisor \
    curl \
    && rm -rf /var/lib/apt/lists/*

# 设置工作目录
WORKDIR /app

# 复制Python依赖文件
COPY requirements-docker.txt .

# 安装Python依赖
RUN pip install --no-cache-dir -r requirements-docker.txt

# 复制后端源代码
COPY backend/ ./backend/
COPY data/ ./data/
COPY migrations/ ./migrations/

# 从前端构建阶段复制构建产物
COPY --from=frontend-builder /frontend/out ./frontend

# 复制Docker配置文件
COPY docker/nginx.conf /etc/nginx/nginx.conf
COPY docker/supervisord.conf /etc/supervisor/conf.d/supervisord.conf
COPY docker/start.sh /start.sh
COPY docker/healthcheck.sh /healthcheck.sh
COPY docker/init-db.sh /init-db.sh

# 创建必要的目录
RUN mkdir -p /var/log/supervisor \
    && mkdir -p /var/log/nginx \
    && mkdir -p /var/run \
    && mkdir -p /app/logs

# 设置权限
RUN chmod +x /start.sh /healthcheck.sh /init-db.sh \
    && chown -R app:app /app \
    && chown -R app:app /app/data \
    && chown app:app /var/log/supervisor

# 暴露端口
EXPOSE 80

# 健康检查
HEALTHCHECK --interval=30s --timeout=30s --start-period=60s --retries=3 \
    CMD /healthcheck.sh

# 启动命令
CMD ["/start.sh"] 